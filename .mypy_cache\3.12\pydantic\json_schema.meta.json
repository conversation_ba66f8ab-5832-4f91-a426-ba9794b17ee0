{"data_mtime": 1757534914, "dep_lines": [46, 46, 46, 46, 46, 46, 46, 62, 21, 39, 42, 44, 46, 55, 56, 57, 64, 1507, 2204, 12, 14, 15, 16, 17, 18, 19, 20, 22, 23, 25, 38, 41, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 20, 5, 5, 5, 20, 20, 20, 5, 10, 10, 10, 10, 5, 10, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._core_utils", "pydantic._internal._decorators", "pydantic._internal._internal_dataclass", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._dataclasses", "collections.abc", "pydantic_core.core_schema", "typing_inspection.introspection", "pydantic.warnings", "pydantic._internal", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.main", "pydantic.root_model", "pydantic.type_adapter", "__future__", "dataclasses", "inspect", "math", "os", "re", "warnings", "collections", "copy", "enum", "typing", "pydantic_core", "typing_extensions", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "datetime", "decimal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "types"], "hash": "dca42a27dabe191d8c7f71022cb6ae1a2fa234fb", "id": "pydantic.json_schema", "ignore_all": true, "interface_hash": "fe2dc7e661de8c4df974f8270ff7b7e159e6ef1c", "mtime": 1757425225, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\pydantic\\json_schema.py", "plugin_data": null, "size": 115430, "suppressed": [], "version_id": "1.17.1"}