{"data_mtime": 1757534916, "dep_lines": [52, 9, 41, 42, 43, 44, 46, 53, 54, 56, 57, 58, 60, 63, 65, 67, 68, 445, 1095, 7, 9, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 36, 38, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 72], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 5, 20, 10, 5, 10, 10, 5, 10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.timing", "_pytest._code", "_pytest.capture", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.outcomes", "_pytest.pathlib", "_pytest.reports", "_pytest.tmpdir", "_pytest.warning_types", "_pytest.pytester_assertions", "_pytest.unraisableexception", "__future__", "collections", "contextlib", "fnmatch", "gc", "importlib", "io", "locale", "os", "pathlib", "platform", "re", "shutil", "subprocess", "sys", "traceback", "typing", "weakref", "iniconfig", "_pytest", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_pytest._code.source", "_typeshed", "_typeshed.importlib", "abc", "enum", "pluggy", "pluggy._hooks", "pluggy._manager", "pluggy._result", "types"], "hash": "82fad30f3d5f5f5018f3b4b6a80daba6a79a45f2", "id": "_pytest.pytester", "ignore_all": true, "interface_hash": "bab531191b2fc02a4319b4da094634f283426d44", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\pytester.py", "plugin_data": null, "size": 61960, "suppressed": ["pexpect"], "version_id": "1.17.1"}