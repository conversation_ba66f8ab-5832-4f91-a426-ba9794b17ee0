{"data_mtime": 1757534915, "dep_lines": [26, 28, 31, 47, 51, 60, 72, 76, 80, 93, 664, 18, 19, 20, 21, 22, 23, 24, 25, 26, 89, 91, 1, 1, 1, 1, 1, 1, 1, 1, 84], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 20, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["concurrent.futures", "tenacity._utils", "tenacity.retry", "tenacity.nap", "tenacity.stop", "tenacity.wait", "tenacity.before", "tenacity.after", "tenacity.before_sleep", "tenacity.asyncio", "tenacity.tornadoweb", "dataclasses", "functools", "sys", "threading", "time", "typing", "warnings", "abc", "concurrent", "types", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "concurrent.futures._base", "enum", "tenacity.asyncio.retry"], "hash": "91354b7aeeaf70f36dbb2114cb17074bc44a5360", "id": "tenacity", "ignore_all": true, "interface_hash": "9ea3e849f326a0038708187b4716a4d1d99df4b3", "mtime": 1757425214, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\tenacity\\__init__.py", "plugin_data": null, "size": 24060, "suppressed": ["tornado"], "version_id": "1.17.1"}