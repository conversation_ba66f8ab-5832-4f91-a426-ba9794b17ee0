{".class": "MypyFile", "_fullname": "mypy_boto3_redshift_data.paginator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DescribeTablePaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "name": "DescribeTablePaginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.DescribeTablePaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.DescribeTableRequestPaginateTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["ConnectedDatabase", "builtins.str"], ["DbUser", "builtins.str"], ["<PERSON><PERSON><PERSON>", "builtins.str"], ["SecretArn", "builtins.str"], ["Table", "builtins.str"], ["WorkgroupName", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of DescribeTablePaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeTableRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTableResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "name": "GetStatementResultPaginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.GetStatementResultRequestPaginateTypeDef", "items": [["Id", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": ["Id"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of GetStatementResultPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetStatementResultRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultV2Paginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2ResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "name": "GetStatementResultV2Paginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2RequestPaginateTypeDef", "items": [["Id", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": ["Id"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of GetStatementResultV2Paginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2ResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetStatementResultV2RequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2RequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultV2ResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2ResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListDatabasesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "name": "ListDatabasesPaginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListDatabasesRequestPaginateTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["DbUser", "builtins.str"], ["SecretArn", "builtins.str"], ["WorkgroupName", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of ListDatabasesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListDatabasesRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListDatabasesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListSchemasPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "name": "ListSchemasPaginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.ListSchemasPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListSchemasRequestPaginateTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["ConnectedDatabase", "builtins.str"], ["DbUser", "builtins.str"], ["SchemaPattern", "builtins.str"], ["SecretArn", "builtins.str"], ["WorkgroupName", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of ListSchemasPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSchemasRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListSchemasResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListStatementsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "name": "ListStatementsPaginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.ListStatementsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListStatementsRequestPaginateTypeDef", "items": [["ClusterIdentifier", "builtins.str"], ["Database", "builtins.str"], ["RoleLevel", "builtins.bool"], ["StatementName", "builtins.str"], ["Status", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.literals.StatusStringType"}], ["WorkgroupName", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of ListStatementsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListStatementsRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListStatementsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListTablesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListTablesResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "name": "ListTablesPaginator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.paginator", "mro": ["mypy_boto3_redshift_data.paginator.ListTablesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.paginator.ListTablesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.paginator.ListTablesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListTablesRequestPaginateTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["ConnectedDatabase", "builtins.str"], ["DbUser", "builtins.str"], ["SchemaPattern", "builtins.str"], ["SecretArn", "builtins.str"], ["TablePattern", "builtins.str"], ["WorkgroupName", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.PaginatorConfigTypeDef"}]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "paginate of ListTablesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListTablesResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.paginator.ListTablesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListTablesRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListTablesRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListTablesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListTablesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PageIterator": {".class": "SymbolTableNode", "cross_ref": "botocore.paginate.PageIterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Paginator": {".class": "SymbolTableNode", "cross_ref": "botocore.paginate.Paginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DescribeTablePaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._DescribeTablePaginatorBase", "line": 77, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "_GetStatementResultPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._GetStatementResultPaginatorBase", "line": 95, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "_GetStatementResultV2PaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._GetStatementResultV2PaginatorBase", "line": 113, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2ResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "_ListDatabasesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._ListDatabasesPaginatorBase", "line": 131, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "_ListSchemasPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._ListSchemasPaginatorBase", "line": 149, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "_ListStatementsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._ListStatementsPaginatorBase", "line": 167, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "_ListTablesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_redshift_data.paginator._ListTablesPaginatorBase", "line": 185, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListTablesResponseTypeDef"}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_redshift_data.paginator.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.paginator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.paginator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.paginator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.paginator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.paginator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.paginator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy_boto3_redshift_data\\paginator.pyi"}