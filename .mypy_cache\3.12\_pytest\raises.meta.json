{"data_mtime": 1757534916, "dep_lines": [21, 515, 20, 22, 23, 27, 1, 3, 5, 7, 8, 9, 18, 31, 33, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 5, 5, 25, 5, 5, 5, 10, 5, 5, 10, 25, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.assertion.util", "_pytest._code", "_pytest.outcomes", "_pytest.warning_types", "collections.abc", "__future__", "abc", "re", "sys", "textwrap", "typing", "warnings", "types", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_pytest.assertion", "_typeshed", "_warnings", "enum"], "hash": "1d45cef60f923b7cf9af79a3d95a335585a6ca3d", "id": "_pytest.raises", "ignore_all": true, "interface_hash": "ffbfb99208336bbf42c31b96d721d80b6aa91655", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\raises.py", "plugin_data": null, "size": 60196, "suppressed": [], "version_id": "1.17.1"}