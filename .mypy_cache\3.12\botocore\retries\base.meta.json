{"data_mtime": 1757534916, "dep_lines": [7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30, 30], "dependencies": ["botocore.retries.standard", "builtins", "_frozen_importlib", "abc", "types", "typing"], "hash": "d132a671b7c0b3b0edc966922cec4374b36c1630", "id": "botocore.retries.base", "ignore_all": true, "interface_hash": "c223a577eda315573c8e5d1d6b8b4792bab5f62a", "mtime": 1757425223, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\botocore-stubs\\retries\\base.pyi", "plugin_data": null, "size": 323, "suppressed": [], "version_id": "1.17.1"}