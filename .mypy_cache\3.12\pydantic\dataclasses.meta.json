{"data_mtime": 1757534914, "dep_lines": [13, 13, 13, 13, 14, 13, 15, 16, 17, 18, 3, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._namespace_utils", "pydantic._internal._typing_extra", "pydantic._internal._dataclasses", "pydantic._internal", "pydantic._migration", "pydantic.config", "pydantic.errors", "pydantic.fields", "__future__", "dataclasses", "sys", "types", "typing", "warnings", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "enum", "pydantic._internal._generate_schema", "pydantic._internal._repr", "pydantic.aliases", "pydantic.types", "re"], "hash": "b55ab263378a20cf6ddb8a17f43f29a0abff9303", "id": "pydantic.dataclasses", "ignore_all": true, "interface_hash": "488cf4fe717e4b37a041d46be0db451de4516f16", "mtime": 1757425225, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\pydantic\\dataclasses.py", "plugin_data": null, "size": 16644, "suppressed": [], "version_id": "1.17.1"}