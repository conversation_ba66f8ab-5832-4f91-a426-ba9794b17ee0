{".class": "MypyFile", "_fullname": "mypy_boto3_redshift_data.client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseClient": {".class": "SymbolTableNode", "cross_ref": "botocore.client.BaseClient", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseClientExceptions": {".class": "SymbolTableNode", "cross_ref": "botocore.errorfactory.BaseClientExceptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BatchExecuteStatementInputTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.BatchExecuteStatementInputTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BatchExecuteStatementOutputTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.BatchExecuteStatementOutputTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BotocoreClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ClientError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CancelStatementRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.CancelStatementRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CancelStatementResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.CancelStatementResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientMeta": {".class": "SymbolTableNode", "cross_ref": "botocore.client.ClientMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeStatementRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.DescribeStatementRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeStatementResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.DescribeStatementResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTablePaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTableRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTableResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.errorfactory.BaseClientExceptions"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.client.Exceptions", "name": "Exceptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.client.Exceptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.client", "mro": ["mypy_boto3_redshift_data.client.Exceptions", "botocore.errorfactory.BaseClientExceptions", "builtins.object"], "names": {".class": "SymbolTable", "ActiveSessionsExceededException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.ActiveSessionsExceededException", "name": "ActiveSessionsExceededException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ActiveStatementsExceededException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.ActiveStatementsExceededException", "name": "ActiveStatementsExceededException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "BatchExecuteStatementException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.BatchExecuteStatementException", "name": "BatchExecuteStatementException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ClientError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.ClientError", "name": "ClientError", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DatabaseConnectionException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.DatabaseConnectionException", "name": "DatabaseConnectionException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ExecuteStatementException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.ExecuteStatementException", "name": "ExecuteStatementException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InternalServerException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.InternalServerException", "name": "InternalServerException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "QueryTimeoutException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.QueryTimeoutException", "name": "QueryTimeoutException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ResourceNotFoundException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.ResourceNotFoundException", "name": "ResourceNotFoundException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ValidationException": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.Exceptions.ValidationException", "name": "ValidationException", "setter_type": null, "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.client.Exceptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.client.Exceptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExecuteStatementInputTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ExecuteStatementInputTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExecuteStatementOutputTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ExecuteStatementOutputTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultV2Paginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultV2RequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2RequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetStatementResultV2ResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2ResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListDatabasesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListDatabasesRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListDatabasesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListSchemasPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListSchemasRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListSchemasResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListStatementsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListStatementsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListStatementsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListTablesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListTablesRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListTablesRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListTablesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.type_defs.ListTablesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedshiftDataAPIServiceClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.client.BaseClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "name": "RedshiftDataAPIServiceClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_redshift_data.client", "mro": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "botocore.client.BaseClient", "builtins.object"], "names": {".class": "SymbolTable", "batch_execute_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.batch_execute_statement", "name": "batch_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.BatchExecuteStatementInputTypeDef", "items": [["Sqls", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["ClientToken", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["Database", "builtins.str"], ["DbUser", "builtins.str"], ["ResultFormat", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.literals.ResultFormatStringType"}], ["SecretArn", "builtins.str"], ["SessionId", "builtins.str"], ["SessionKeepAliveSeconds", "builtins.int"], ["StatementName", "builtins.str"], ["WithEvent", "builtins.bool"], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": ["Sqls"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_execute_statement of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.BatchExecuteStatementOutputTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "can_paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.can_paginate", "name": "can_paginate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_paginate of RedshiftDataAPIServiceClient", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancel_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.cancel_statement", "name": "cancel_statement", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.CancelStatementRequestTypeDef", "items": [["Id", "builtins.str"]], "readonly_keys": [], "required_keys": ["Id"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_statement of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.CancelStatementResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "describe_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.describe_statement", "name": "describe_statement", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.DescribeStatementRequestTypeDef", "items": [["Id", "builtins.str"]], "readonly_keys": [], "required_keys": ["Id"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_statement of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.DescribeStatementResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "describe_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.describe_table", "name": "describe_table", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.DescribeTableRequestTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["ConnectedDatabase", "builtins.str"], ["DbUser", "builtins.str"], ["MaxResults", "builtins.int"], ["NextToken", "builtins.str"], ["<PERSON><PERSON><PERSON>", "builtins.str"], ["SecretArn", "builtins.str"], ["Table", "builtins.str"], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_table of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.DescribeTableResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.exceptions", "name": "exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exceptions of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.client.Exceptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.exceptions", "name": "exceptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exceptions of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.client.Exceptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execute_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.execute_statement", "name": "execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ExecuteStatementInputTypeDef", "items": [["Sql", "builtins.str"], ["ClientToken", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["Database", "builtins.str"], ["DbUser", "builtins.str"], ["Parameters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.SqlParameterTypeDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["ResultFormat", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.literals.ResultFormatStringType"}], ["SecretArn", "builtins.str"], ["SessionId", "builtins.str"], ["SessionKeepAliveSeconds", "builtins.int"], ["StatementName", "builtins.str"], ["WithEvent", "builtins.bool"], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": ["Sql"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_statement of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ExecuteStatementOutputTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "generate_presigned_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "ClientMethod", "Params", "ExpiresIn", "HttpMethod"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.generate_presigned_url", "name": "generate_presigned_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "ClientMethod", "Params", "ExpiresIn", "HttpMethod"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.int", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_presigned_url of RedshiftDataAPIServiceClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_paginator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_table"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_table"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "get_statement_result"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "get_statement_result"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "get_statement_result_v2"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "get_statement_result_v2"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_databases"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_databases"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_schemas"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_schemas"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_statements"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_statements"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_tables"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_paginator", "name": "get_paginator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_tables"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_table"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "get_statement_result"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "get_statement_result_v2"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_databases"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_schemas"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_statements"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "list_tables"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of RedshiftDataAPIServiceClient", "ret_type": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_statement_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_statement_result", "name": "get_statement_result", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.GetStatementResultRequestTypeDef", "items": [["Id", "builtins.str"], ["NextToken", "builtins.str"]], "readonly_keys": [], "required_keys": ["Id"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_statement_result of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "get_statement_result_v2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.get_statement_result_v2", "name": "get_statement_result_v2", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2RequestTypeDef", "items": [["Id", "builtins.str"], ["NextToken", "builtins.str"]], "readonly_keys": [], "required_keys": ["Id"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_statement_result_v2 of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.GetStatementResultV2ResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "list_databases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.list_databases", "name": "list_databases", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListDatabasesRequestTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["DbUser", "builtins.str"], ["MaxResults", "builtins.int"], ["NextToken", "builtins.str"], ["SecretArn", "builtins.str"], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_databases of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListDatabasesResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "list_schemas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.list_schemas", "name": "list_schemas", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListSchemasRequestTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["ConnectedDatabase", "builtins.str"], ["DbUser", "builtins.str"], ["MaxResults", "builtins.int"], ["NextToken", "builtins.str"], ["SchemaPattern", "builtins.str"], ["SecretArn", "builtins.str"], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_schemas of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListSchemasResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "list_statements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.list_statements", "name": "list_statements", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListStatementsRequestTypeDef", "items": [["ClusterIdentifier", "builtins.str"], ["Database", "builtins.str"], ["MaxResults", "builtins.int"], ["NextToken", "builtins.str"], ["RoleLevel", "builtins.bool"], ["StatementName", "builtins.str"], ["Status", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.literals.StatusStringType"}], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_statements of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListStatementsResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "list_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.list_tables", "name": "list_tables", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", {".class": "TypedDictType", "fallback": "mypy_boto3_redshift_data.type_defs.ListTablesRequestTypeDef", "items": [["Database", "builtins.str"], ["ClusterIdentifier", "builtins.str"], ["ConnectedDatabase", "builtins.str"], ["DbUser", "builtins.str"], ["MaxResults", "builtins.int"], ["NextToken", "builtins.str"], ["SchemaPattern", "builtins.str"], ["SecretArn", "builtins.str"], ["TablePattern", "builtins.str"], ["WorkgroupName", "builtins.str"]], "readonly_keys": [], "required_keys": ["Database"]}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_tables of RedshiftDataAPIServiceClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_redshift_data.type_defs.ListTablesResponseTypeDef"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.meta", "name": "meta", "setter_type": null, "type": "botocore.client.ClientMeta"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Type": {".class": "SymbolTableNode", "cross_ref": "builtins.type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_redshift_data.client.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy_boto3_redshift_data\\client.pyi"}