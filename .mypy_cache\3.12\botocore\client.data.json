{".class": "MypyFile", "_fullname": "botocore.client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AUTH_TYPE_MAPS": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.AUTH_TYPE_MAPS", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.client.BaseClient", "name": "BaseClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.client.BaseClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.client", "mro": ["botocore.client.BaseClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "serializer", "endpoint", "response_parser", "event_emitter", "request_signer", "service_model", "loader", "client_config", "partition", "exceptions_factory", "endpoint_ruleset_resolver", "user_agent_creator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.BaseClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "serializer", "endpoint", "response_parser", "event_emitter", "request_signer", "service_model", "loader", "client_config", "partition", "exceptions_factory", "endpoint_ruleset_resolver", "user_agent_creator"], "arg_types": ["botocore.client.BaseClient", "botocore.serialize.Serializer", "builtins.str", "botocore.parsers.ResponseParser", "botocore.hooks.BaseEventHooks", "botocore.signers.RequestSigner", "botocore.model.ServiceModel", "botocore.loaders.Loader", "botocore.config.Config", "builtins.str", "botocore.errorfactory.ClientExceptionsFactory", {".class": "UnionType", "items": ["botocore.regions.EndpointRulesetResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.useragent.UserAgentString", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.BaseClient.can_paginate", "name": "can_paginate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["botocore.client.BaseClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_paginate of BaseClient", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.BaseClient.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.BaseClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of BaseClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.BaseClient.exceptions", "name": "exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.BaseClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exceptions of BaseClient", "ret_type": "botocore.errorfactory.BaseClientExceptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.BaseClient.exceptions", "name": "exceptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.BaseClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exceptions of BaseClient", "ret_type": "botocore.errorfactory.BaseClientExceptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_paginator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.BaseClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["botocore.client.BaseClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_paginator of BaseClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "botocore.paginate.Paginator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_waiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.BaseClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["botocore.client.BaseClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_waiter of BaseClient", "ret_type": "botocore.waiter.Waiter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "meta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.BaseClient.meta", "name": "meta", "setter_type": null, "type": "botocore.client.ClientMeta"}}, "waiter_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "botocore.client.BaseClient.waiter_names", "name": "waiter_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.BaseClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "waiter_names of BaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "botocore.client.BaseClient.waiter_names", "name": "waiter_names", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "botocore.utils.CachedProperty"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.client.BaseClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.client.BaseClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseClientExceptions": {".class": "SymbolTableNode", "cross_ref": "botocore.errorfactory.BaseClientExceptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEndpointResolver": {".class": "SymbolTableNode", "cross_ref": "botocore.regions.BaseEndpointResolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CachedProperty": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.CachedProperty", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientArgsCreator": {".class": "SymbolTableNode", "cross_ref": "botocore.args.ClientArgsCreator", "kind": "Gdef"}, "ClientCreator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.client.ClientCreator", "name": "ClientCreator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.client.ClientCreator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.client", "mro": ["botocore.client.ClientCreator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "loader", "endpoint_resolver", "user_agent", "event_emitter", "retry_handler_factory", "retry_config_translator", "response_parser_factory", "exceptions_factory", "config_store", "user_agent_creator", "auth_token_resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientCreator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "loader", "endpoint_resolver", "user_agent", "event_emitter", "retry_handler_factory", "retry_config_translator", "response_parser_factory", "exceptions_factory", "config_store", "user_agent_creator", "auth_token_resolver"], "arg_types": ["botocore.client.ClientCreator", "botocore.loaders.Loader", "botocore.regions.BaseEndpointResolver", "builtins.str", "botocore.hooks.BaseEventHooks", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["botocore.parsers.ResponseParserFactory", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.errorfactory.ClientExceptionsFactory", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.configprovider.ConfigValueStore", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.useragent.UserAgentString", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClientCreator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "service_name", "region_name", "is_secure", "endpoint_url", "verify", "credentials", "scoped_config", "api_version", "client_config", "auth_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientCreator.create_client", "name": "create_client", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "service_name", "region_name", "is_secure", "endpoint_url", "verify", "credentials", "scoped_config", "api_version", "client_config", "auth_token"], "arg_types": ["botocore.client.ClientCreator", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_client of ClientCreator", "ret_type": "botocore.client.BaseClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_client_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "service_name", "api_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientCreator.create_client_class", "name": "create_client_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "service_name", "api_version"], "arg_types": ["botocore.client.ClientCreator", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_client_class of ClientCreator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.client.ClientCreator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.client.ClientCreator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientEndpointBridge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.client.ClientEndpointBridge", "name": "ClientEndpointBridge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.client.ClientEndpointBridge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.client", "mro": ["botocore.client.ClientEndpointBridge", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.client.ClientEndpointBridge.DEFAULT_ENDPOINT", "name": "DEFAULT_ENDPOINT", "setter_type": null, "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint_resolver", "scoped_config", "client_config", "default_endpoint", "service_signing_name", "config_store", "service_signature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientEndpointBridge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint_resolver", "scoped_config", "client_config", "default_endpoint", "service_signing_name", "config_store", "service_signature_version"], "arg_types": ["botocore.client.ClientEndpointBridge", "botocore.regions.BaseEndpointResolver", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.configprovider.ConfigValueStore", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClientEndpointBridge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientEndpointBridge.client_config", "name": "client_config", "setter_type": null, "type": "botocore.config.Config"}}, "config_store": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientEndpointBridge.config_store", "name": "config_store", "setter_type": null, "type": "botocore.configprovider.ConfigValueStore"}}, "default_endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientEndpointBridge.default_endpoint", "name": "default_endpoint", "setter_type": null, "type": "builtins.str"}}, "endpoint_resolver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientEndpointBridge.endpoint_resolver", "name": "endpoint_resolver", "setter_type": null, "type": "botocore.regions.BaseEndpointResolver"}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "service_name", "region_name", "endpoint_url", "is_secure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientEndpointBridge.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "service_name", "region_name", "endpoint_url", "is_secure"], "arg_types": ["botocore.client.ClientEndpointBridge", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve of ClientEndpointBridge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolver_uses_builtin_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientEndpointBridge.resolver_uses_builtin_data", "name": "resolver_uses_builtin_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientEndpointBridge"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolver_uses_builtin_data of ClientEndpointBridge", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scoped_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientEndpointBridge.scoped_config", "name": "scoped_config", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "service_signing_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientEndpointBridge.service_signing_name", "name": "service_signing_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.client.ClientEndpointBridge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.client.ClientEndpointBridge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ClientError", "kind": "Gdef"}, "ClientExceptionsFactory": {".class": "SymbolTableNode", "cross_ref": "botocore.errorfactory.ClientExceptionsFactory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.client.ClientMeta", "name": "ClientMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.client.ClientMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.client", "mro": ["botocore.client.ClientMeta", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "events", "client_config", "endpoint_url", "service_model", "method_to_api_mapping", "partition"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.client.ClientMeta.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "events", "client_config", "endpoint_url", "service_model", "method_to_api_mapping", "partition"], "arg_types": ["botocore.client.ClientMeta", "botocore.hooks.BaseEventHooks", "botocore.config.Config", "builtins.str", "botocore.model.ServiceModel", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClientMeta", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.ClientMeta.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "config of ClientMeta", "ret_type": "botocore.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.config", "name": "config", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "config of ClientMeta", "ret_type": "botocore.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "endpoint_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.ClientMeta.endpoint_url", "name": "endpoint_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "endpoint_url of ClientMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.endpoint_url", "name": "endpoint_url", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "endpoint_url of ClientMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "events": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.events", "name": "events", "setter_type": null, "type": "botocore.hooks.BaseEventHooks"}}, "method_to_api_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.ClientMeta.method_to_api_mapping", "name": "method_to_api_mapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "method_to_api_mapping of ClientMeta", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.method_to_api_mapping", "name": "method_to_api_mapping", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "method_to_api_mapping of ClientMeta", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "partition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.ClientMeta.partition", "name": "partition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition of ClientMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.partition", "name": "partition", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition of ClientMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "region_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.ClientMeta.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "region_name of ClientMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.region_name", "name": "region_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "region_name of ClientMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "service_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "botocore.client.ClientMeta.service_model", "name": "service_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "service_model of ClientMeta", "ret_type": "botocore.model.ServiceModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.client.ClientMeta.service_model", "name": "service_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.client.ClientMeta"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "service_model of ClientMeta", "ret_type": "botocore.model.ServiceModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.client.ClientMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.client.ClientMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "botocore.config.Config", "kind": "Gdef"}, "ConfigValueStore": {".class": "SymbolTableNode", "cross_ref": "botocore.configprovider.ConfigValueStore", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DataNotFoundError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.DataNotFoundError", "kind": "Gdef"}, "EndpointDiscoveryHandler": {".class": "SymbolTableNode", "cross_ref": "botocore.discovery.EndpointDiscoveryHandler", "kind": "Gdef"}, "EndpointDiscoveryManager": {".class": "SymbolTableNode", "cross_ref": "botocore.discovery.EndpointDiscoveryManager", "kind": "Gdef"}, "EndpointRulesetResolver": {".class": "SymbolTableNode", "cross_ref": "botocore.regions.EndpointRulesetResolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HistoryRecorder": {".class": "SymbolTableNode", "cross_ref": "botocore.history.HistoryRecorder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InvalidEndpointDiscoveryConfigurationError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError", "kind": "Gdef"}, "Loader": {".class": "SymbolTableNode", "cross_ref": "botocore.loaders.Loader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OperationNotPageableError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.OperationNotPageableError", "kind": "Gdef"}, "Paginator": {".class": "SymbolTableNode", "cross_ref": "botocore.paginate.Paginator", "kind": "Gdef"}, "RequestSigner": {".class": "SymbolTableNode", "cross_ref": "botocore.signers.RequestSigner", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResponseParser": {".class": "SymbolTableNode", "cross_ref": "botocore.parsers.ResponseParser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResponseParserFactory": {".class": "SymbolTableNode", "cross_ref": "botocore.parsers.ResponseParserFactory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "S3ArnParamHandler": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.S3ArnParamHandler", "kind": "Gdef"}, "S3ControlArnParamHandler": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.S3ControlArnParamHandler", "kind": "Gdef"}, "S3ControlEndpointSetter": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.S3ControlEndpointSetter", "kind": "Gdef"}, "S3EndpointSetter": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.S3EndpointSetter", "kind": "Gdef"}, "S3RegionRedirector": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.S3RegionRedirector", "kind": "Gdef"}, "Serializer": {".class": "SymbolTableNode", "cross_ref": "botocore.serialize.Serializer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServiceModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceModel", "kind": "Gdef"}, "UnknownSignatureVersionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnknownSignatureVersionError", "kind": "Gdef"}, "UserAgentString": {".class": "SymbolTableNode", "cross_ref": "botocore.useragent.UserAgentString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Waiter": {".class": "SymbolTableNode", "cross_ref": "botocore.waiter.Waiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "adaptive": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.adaptive", "kind": "Gdef"}, "block_endpoint_discovery_required_operations": {".class": "SymbolTableNode", "cross_ref": "botocore.discovery.block_endpoint_discovery_required_operations", "kind": "Gdef"}, "ensure_boolean": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.ensure_boolean", "kind": "Gdef"}, "first_non_none_response": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.first_non_none_response", "kind": "Gdef"}, "get_global_history_recorder": {".class": "SymbolTableNode", "cross_ref": "botocore.history.get_global_history_recorder", "kind": "Gdef"}, "get_service_module_name": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.get_service_module_name", "kind": "Gdef"}, "history_recorder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.client.history_recorder", "name": "history_recorder", "setter_type": null, "type": "botocore.history.HistoryRecorder"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.client.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "prepare_request_dict": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.prepare_request_dict", "kind": "Gdef"}, "standard": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.standard", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\botocore-stubs\\client.pyi"}