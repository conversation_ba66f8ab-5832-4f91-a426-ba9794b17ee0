{"data_mtime": 1757534916, "dep_lines": [4, 1, 2, 10, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["aws_lambda_powertools.shared.version", "logging", "os", "botocore", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "5316684da59479b5d7fe90c92d23b5e1b84e1a2a", "id": "aws_lambda_powertools.shared.user_agent", "ignore_all": true, "interface_hash": "df820fcb0a3866eee64d753551b2c30481e9bed9", "mtime": 1757425223, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\aws_lambda_powertools\\shared\\user_agent.py", "plugin_data": null, "size": 7081, "suppressed": [], "version_id": "1.17.1"}