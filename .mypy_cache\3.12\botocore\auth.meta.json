{"data_mtime": 1757534916, "dep_lines": [17, 7, 8, 11, 13, 14, 16, 18, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["botocore.crt.auth", "collections.abc", "http.client", "urllib.parse", "botocore.awsrequest", "botocore.compat", "botocore.credentials", "botocore.utils", "logging", "typing", "builtins", "_frozen_importlib", "abc", "email", "email.message", "http", "types", "urllib"], "hash": "1ad2f3a2d6be9432ec4c6b0e5a365be6b919a499", "id": "botocore.auth", "ignore_all": true, "interface_hash": "97b8a13515744f38b02120285ce95bef23b61c1b", "mtime": 1757425223, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\botocore-stubs\\auth.pyi", "plugin_data": null, "size": 5591, "suppressed": [], "version_id": "1.17.1"}