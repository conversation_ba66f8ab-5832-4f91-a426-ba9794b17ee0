{"data_mtime": 1757534914, "dep_lines": [24, 24, 24, 24, 24, 24, 24, 25, 9, 20, 21, 23, 24, 26, 27, 28, 29, 30, 3, 5, 6, 7, 8, 10, 12, 14, 16, 17, 18, 20, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generics", "pydantic._internal._internal_dataclass", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal._namespace_utils", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.types", "pydantic._internal", "pydantic.aliases", "pydantic.config", "pydantic.errors", "pydantic.json_schema", "pydantic.warnings", "__future__", "dataclasses", "inspect", "sys", "typing", "copy", "functools", "warnings", "annotated_types", "typing_extensions", "pydantic_core", "typing_inspection", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "enum", "pydantic_core._pydantic_core", "re", "types"], "hash": "fcfcd8a3600400a929c3dc0a4d2a761e6c9c96cf", "id": "pydantic.fields", "ignore_all": true, "interface_hash": "9134e747594b6a4f1c484b13c27cd50def582f88", "mtime": 1757425225, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\pydantic\\fields.py", "plugin_data": null, "size": 64416, "suppressed": [], "version_id": "1.17.1"}