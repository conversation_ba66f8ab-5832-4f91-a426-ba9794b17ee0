{"data_mtime": 1757534915, "dep_lines": [2, 3, 1, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["asyncio.events", "collections.abc", "sys", "<PERSON><PERSON><PERSON>", "types", "typing", "typing_extensions", "builtins", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio"], "hash": "51402d5abdfc452ead7874ac121995b3c4d2e3a4", "id": "_asyncio", "ignore_all": true, "interface_hash": "d80665e485ae33a3c20dc920ef8810d46692e711", "mtime": 1757425219, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\_asyncio.pyi", "plugin_data": null, "size": 4933, "suppressed": [], "version_id": "1.17.1"}