{".class": "MypyFile", "_fullname": "tests.integration.test_lambda_integration", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "TestLambdaIntegration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration", "name": "TestLambdaIntegration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.integration.test_lambda_integration", "mro": ["tests.integration.test_lambda_integration.TestLambdaIntegration", "builtins.object"], "names": {".class": "SymbolTable", "create_sqs_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "xml_message", "message_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.create_sqs_event", "name": "create_sqs_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "xml_message", "message_id"], "arg_types": ["tests.integration.test_lambda_integration.TestLambdaIntegration", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_sqs_event of TestLambdaIntegration", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lambda_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.lambda_url", "name": "lambda_url", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.lambda_url", "name": "lambda_url", "setter_type": null, "type": "_pytest.fixtures.FixtureFunctionDefinition"}}}, "sample_busied_out_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.sample_busied_out_xml", "name": "sample_busied_out_xml", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.sample_busied_out_xml", "name": "sample_busied_out_xml", "setter_type": null, "type": "_pytest.fixtures.FixtureFunctionDefinition"}}}, "sample_login_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.sample_login_xml", "name": "sample_login_xml", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.sample_login_xml", "name": "sample_login_xml", "setter_type": null, "type": "_pytest.fixtures.FixtureFunctionDefinition"}}}, "test_batch_event_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "lambda_url", "sample_login_xml", "sample_busied_out_xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_batch_event_processing", "name": "test_batch_event_processing", "type": null}}, "test_busied_out_event_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lambda_url", "sample_busied_out_xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_busied_out_event_processing", "name": "test_busied_out_event_processing", "type": null}}, "test_database_connectivity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lambda_url", "sample_login_xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_database_connectivity", "name": "test_database_connectivity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_database_connectivity", "name": "test_database_connectivity", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_invalid_xml_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lambda_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_invalid_xml_handling", "name": "test_invalid_xml_handling", "type": null}}, "test_lambda_response_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lambda_url", "sample_login_xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_lambda_response_format", "name": "test_lambda_response_format", "type": null}}, "test_lambda_timeout_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lambda_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_lambda_timeout_handling", "name": "test_lambda_timeout_handling", "type": null}}, "test_login_event_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lambda_url", "sample_login_xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_login_event_processing", "name": "test_login_event_processing", "type": null}}, "test_missing_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lambda_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.test_missing_required_fields", "name": "test_missing_required_fields", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.integration.test_lambda_integration.TestLambdaIntegration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.integration.test_lambda_integration.TestLambdaIntegration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.integration.test_lambda_integration.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.integration.test_lambda_integration.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.integration.test_lambda_integration.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.integration.test_lambda_integration.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.integration.test_lambda_integration.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.integration.test_lambda_integration.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}}, "path": "lambdas\\agent-event-processor\\tests\\integration\\test_lambda_integration.py"}