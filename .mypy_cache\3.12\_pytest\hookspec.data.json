{".class": "MypyFile", "_fullname": "_pytest.hookspec", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CallInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest.runner.CallInfo", "kind": "Gdef"}, "Class": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Class", "kind": "Gdef"}, "CollectReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.CollectReport", "kind": "Gdef"}, "Collector": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Collector", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "ExceptionRepr": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionRepr", "kind": "Gdef"}, "Exit": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.Exit", "kind": "Gdef"}, "ExitCode": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ExitCode", "kind": "Gdef"}, "FixtureDef": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FixtureDef", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Function", "kind": "Gdef"}, "HOOK_LEGACY_PATH_ARG": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.HOOK_LEGACY_PATH_ARG", "kind": "Gdef"}, "HookspecMarker": {".class": "SymbolTableNode", "cross_ref": "pluggy._hooks.HookspecMarker", "kind": "Gdef"}, "Item": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Item", "kind": "Gdef"}, "LEGACY_PATH": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.LEGACY_PATH", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Metafunc": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Metafunc", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Module", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PytestPluginManager": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.PytestPluginManager", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "SubRequest": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.SubRequest", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TerminalReporter": {".class": "SymbolTableNode", "cross_ref": "_pytest.terminal.TerminalReporter", "kind": "Gdef"}, "TestReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.TestReport", "kind": "Gdef"}, "TestShortLogReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.terminal.TestShortLogReport", "kind": "Gdef"}, "_PluggyPlugin": {".class": "SymbolTableNode", "cross_ref": "_pytest.config._PluggyPlugin", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.hookspec.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.hookspec.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.hookspec.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.hookspec.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.hookspec.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.hookspec.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "hookspec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.hookspec.hookspec", "name": "hookspec", "setter_type": null, "type": "pluggy._hooks.HookspecMarker"}}, "pdb": {".class": "SymbolTableNode", "cross_ref": "pdb", "kind": "Gdef"}, "pytest_addhooks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pluginmanager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_addhooks", "name": "pytest_addhooks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pluginmanager"], "arg_types": ["_pytest.config.PytestPluginManager"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_addhooks", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_addhooks", "name": "pytest_addhooks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pluginmanager"], "arg_types": ["_pytest.config.PytestPluginManager"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_addhooks", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["parser", "pluginmanager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["parser", "pluginmanager"], "arg_types": ["_pytest.config.argparsing.Parser", "_pytest.config.PytestPluginManager"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_addoption", "name": "pytest_addoption", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["parser", "pluginmanager"], "arg_types": ["_pytest.config.argparsing.Parser", "_pytest.config.PytestPluginManager"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_assertion_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["item", "lineno", "orig", "expl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_assertion_pass", "name": "pytest_assertion_pass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["item", "lineno", "orig", "expl"], "arg_types": ["_pytest.nodes.Item", "builtins.int", "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_assertion_pass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_assertrepr_compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["config", "op", "left", "right"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_assertrepr_compare", "name": "pytest_assertrepr_compare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["config", "op", "left", "right"], "arg_types": ["_pytest.config.Config", "builtins.str", "builtins.object", "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_assertrepr_compare", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_cmdline_main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_cmdline_main", "name": "pytest_cmdline_main", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_cmdline_main", "ret_type": {".class": "UnionType", "items": ["_pytest.config.ExitCode", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_cmdline_main", "name": "pytest_cmdline_main", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_cmdline_main", "ret_type": {".class": "UnionType", "items": ["_pytest.config.ExitCode", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_cmdline_parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pluginmanager", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_cmdline_parse", "name": "pytest_cmdline_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pluginmanager", "args"], "arg_types": ["_pytest.config.PytestPluginManager", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_cmdline_parse", "ret_type": {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_cmdline_parse", "name": "pytest_cmdline_parse", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pluginmanager", "args"], "arg_types": ["_pytest.config.PytestPluginManager", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_cmdline_parse", "ret_type": {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_collect_directory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_collect_directory", "name": "pytest_collect_directory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "parent"], "arg_types": ["pathlib.Path", "_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collect_directory", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Collector", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_collect_directory", "name": "pytest_collect_directory", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "parent"], "arg_types": ["pathlib.Path", "_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collect_directory", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Collector", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_collect_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["file_path", "path", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_collect_file", "name": "pytest_collect_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["file_path", "path", "parent"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, "_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collect_file", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Collector", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_collect_file", "name": "pytest_collect_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["file_path", "path", "parent"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, "_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collect_file", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Collector", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_collection", "name": "pytest_collection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collection", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_collection", "name": "pytest_collection", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collection", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_collection_finish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_collection_finish", "name": "pytest_collection_finish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collection_finish", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_collection_modifyitems": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["session", "config", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_collection_modifyitems", "name": "pytest_collection_modifyitems", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["session", "config", "items"], "arg_types": ["_pytest.main.Session", "_pytest.config.Config", {".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collection_modifyitems", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_collectreport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["report"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_collectreport", "name": "pytest_collectreport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["report"], "arg_types": ["_pytest.reports.CollectReport"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collectreport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_collectstart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["collector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_collectstart", "name": "pytest_collectstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["collector"], "arg_types": ["_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_collectstart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_configure", "name": "pytest_configure", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_deselected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_deselected", "name": "pytest_deselected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["items"], "arg_types": [{".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_deselected", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_enter_pdb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "pdb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_enter_pdb", "name": "pytest_enter_pdb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "pdb"], "arg_types": ["_pytest.config.Config", "pdb.Pdb"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_enter_pdb", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_exception_interact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["node", "call", "report"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_exception_interact", "name": "pytest_exception_interact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["node", "call", "report"], "arg_types": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.runner.CallInfo"}, {".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_exception_interact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_fixture_post_finalizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_fixture_post_finalizer", "name": "pytest_fixture_post_finalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_fixture_post_finalizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_fixture_setup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_fixture_setup", "name": "pytest_fixture_setup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_fixture_setup", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_fixture_setup", "name": "pytest_fixture_setup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_fixture_setup", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_generate_tests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["metafunc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_generate_tests", "name": "pytest_generate_tests", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["metafunc"], "arg_types": ["_pytest.python.Metafunc"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_generate_tests", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_ignore_collect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["collection_path", "path", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_ignore_collect", "name": "pytest_ignore_collect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["collection_path", "path", "config"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, "_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_ignore_collect", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_ignore_collect", "name": "pytest_ignore_collect", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["collection_path", "path", "config"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, "_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_ignore_collect", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_internalerror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["excrepr", "excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_internalerror", "name": "pytest_internalerror", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["excrepr", "excinfo"], "arg_types": ["_pytest._code.code.ExceptionRepr", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_internalerror", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_itemcollected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_itemcollected", "name": "pytest_itemcollected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_itemcollected", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_keyboard_interrupt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_keyboard_interrupt", "name": "pytest_keyboard_interrupt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["excinfo"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.KeyboardInterrupt", "_pytest.outcomes.Exit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_keyboard_interrupt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_leave_pdb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "pdb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_leave_pdb", "name": "pytest_leave_pdb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "pdb"], "arg_types": ["_pytest.config.Config", "pdb.Pdb"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_leave_pdb", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_load_initial_conftests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["early_config", "parser", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_load_initial_conftests", "name": "pytest_load_initial_conftests", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["early_config", "parser", "args"], "arg_types": ["_pytest.config.Config", "_pytest.config.argparsing.Parser", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_load_initial_conftests", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_make_collect_report": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["collector"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_make_collect_report", "name": "pytest_make_collect_report", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["collector"], "arg_types": ["_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_make_collect_report", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.CollectReport", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_make_collect_report", "name": "pytest_make_collect_report", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["collector"], "arg_types": ["_pytest.nodes.Collector"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_make_collect_report", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.CollectReport", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_make_parametrize_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["config", "val", "argname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_make_parametrize_id", "name": "pytest_make_parametrize_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "val", "argname"], "arg_types": ["_pytest.config.Config", "builtins.object", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_make_parametrize_id", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_make_parametrize_id", "name": "pytest_make_parametrize_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "val", "argname"], "arg_types": ["_pytest.config.Config", "builtins.object", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_make_parametrize_id", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_markeval_namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_markeval_namespace", "name": "pytest_markeval_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_markeval_namespace", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_plugin_registered": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["plugin", "plugin_name", "manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_plugin_registered", "name": "pytest_plugin_registered", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["plugin", "plugin_name", "manager"], "arg_types": ["builtins.object", "builtins.str", "_pytest.config.PytestPluginManager"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_plugin_registered", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_plugin_registered", "name": "pytest_plugin_registered", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["plugin", "plugin_name", "manager"], "arg_types": ["builtins.object", "builtins.str", "_pytest.config.PytestPluginManager"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_plugin_registered", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_pycollect_makeitem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["collector", "name", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_pycollect_makeitem", "name": "pytest_pycollect_makeitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["collector", "name", "obj"], "arg_types": [{".class": "UnionType", "items": ["_pytest.python.Module", "_pytest.python.Class"], "uses_pep604_syntax": true}, "builtins.str", "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makeitem", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "_pytest.nodes.Item", "_pytest.nodes.Collector", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_pycollect_makeitem", "name": "pytest_pycollect_makeitem", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["collector", "name", "obj"], "arg_types": [{".class": "UnionType", "items": ["_pytest.python.Module", "_pytest.python.Class"], "uses_pep604_syntax": true}, "builtins.str", "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makeitem", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "_pytest.nodes.Item", "_pytest.nodes.Collector", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_pycollect_makemodule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["module_path", "path", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_pycollect_makemodule", "name": "pytest_pycollect_makemodule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["module_path", "path", "parent"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makemodule", "ret_type": {".class": "UnionType", "items": ["_pytest.python.Module", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_pycollect_makemodule", "name": "pytest_pycollect_makemodule", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["module_path", "path", "parent"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_pycollect_makemodule", "ret_type": {".class": "UnionType", "items": ["_pytest.python.Module", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_pyfunc_call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pyfuncitem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_pyfunc_call", "name": "pytest_pyfunc_call", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyfuncitem"], "arg_types": ["_pytest.python.Function"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_pyfunc_call", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_pyfunc_call", "name": "pytest_pyfunc_call", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyfuncitem"], "arg_types": ["_pytest.python.Function"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_pyfunc_call", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_report_collectionfinish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["config", "start_path", "startdir", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_report_collectionfinish", "name": "pytest_report_collectionfinish", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["config", "start_path", "startdir", "items"], "arg_types": ["_pytest.config.Config", "pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_collectionfinish", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_report_collectionfinish", "name": "pytest_report_collectionfinish", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["config", "start_path", "startdir", "items"], "arg_types": ["_pytest.config.Config", "pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_collectionfinish", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_report_from_serializable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_report_from_serializable", "name": "pytest_report_from_serializable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "data"], "arg_types": ["_pytest.config.Config", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_from_serializable", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_report_from_serializable", "name": "pytest_report_from_serializable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "data"], "arg_types": ["_pytest.config.Config", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_from_serializable", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_report_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["config", "start_path", "startdir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_report_header", "name": "pytest_report_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "start_path", "startdir"], "arg_types": ["_pytest.config.Config", "pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_header", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_report_header", "name": "pytest_report_header", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "start_path", "startdir"], "arg_types": ["_pytest.config.Config", "pathlib.Path", {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_header", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_report_teststatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["report", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_report_teststatus", "name": "pytest_report_teststatus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["report", "config"], "arg_types": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"], "uses_pep604_syntax": true}, "_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_teststatus", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.terminal.TestShortLogReport"}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_report_teststatus", "name": "pytest_report_teststatus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["report", "config"], "arg_types": [{".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"], "uses_pep604_syntax": true}, "_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_teststatus", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.terminal.TestShortLogReport"}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_report_to_serializable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "report"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_report_to_serializable", "name": "pytest_report_to_serializable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "report"], "arg_types": ["_pytest.config.Config", {".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_to_serializable", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_report_to_serializable", "name": "pytest_report_to_serializable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "report"], "arg_types": ["_pytest.config.Config", {".class": "UnionType", "items": ["_pytest.reports.CollectReport", "_pytest.reports.TestReport"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_report_to_serializable", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_runtest_call", "name": "pytest_runtest_call", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_call", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_logfinish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nodeid", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_runtest_logfinish", "name": "pytest_runtest_logfinish", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["nodeid", "location"], "arg_types": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_logfinish", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_logreport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["report"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_runtest_logreport", "name": "pytest_runtest_logreport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["report"], "arg_types": ["_pytest.reports.TestReport"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_logreport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_logstart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nodeid", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_runtest_logstart", "name": "pytest_runtest_logstart", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["nodeid", "location"], "arg_types": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_logstart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_makereport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_runtest_makereport", "name": "pytest_runtest_makereport", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "call"], "arg_types": ["_pytest.nodes.Item", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_pytest.runner.CallInfo"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_makereport", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.TestReport", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_runtest_makereport", "name": "pytest_runtest_makereport", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "call"], "arg_types": ["_pytest.nodes.Item", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_pytest.runner.CallInfo"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_makereport", "ret_type": {".class": "UnionType", "items": ["_pytest.reports.TestReport", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_protocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "nextitem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_runtest_protocol", "name": "pytest_runtest_protocol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "nextitem"], "arg_types": ["_pytest.nodes.Item", {".class": "UnionType", "items": ["_pytest.nodes.Item", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_protocol", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_runtest_protocol", "name": "pytest_runtest_protocol", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "nextitem"], "arg_types": ["_pytest.nodes.Item", {".class": "UnionType", "items": ["_pytest.nodes.Item", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_protocol", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_runtest_setup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_runtest_setup", "name": "pytest_runtest_setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["_pytest.nodes.Item"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_setup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtest_teardown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "nextitem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_runtest_teardown", "name": "pytest_runtest_teardown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "nextitem"], "arg_types": ["_pytest.nodes.Item", {".class": "UnionType", "items": ["_pytest.nodes.Item", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtest_teardown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_runtestloop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_runtestloop", "name": "pytest_runtestloop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtestloop", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_runtestloop", "name": "pytest_runtestloop", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_runtestloop", "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest_sessionfinish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["session", "exitstatus"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_sessionfinish", "name": "pytest_sessionfinish", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["session", "exitstatus"], "arg_types": ["_pytest.main.Session", {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_sessionfinish", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_sessionstart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_sessionstart", "name": "pytest_sessionstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_sessionstart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_terminal_summary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["terminalreporter", "exitstatus", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_terminal_summary", "name": "pytest_terminal_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["terminalreporter", "exitstatus", "config"], "arg_types": ["_pytest.terminal.TerminalReporter", "_pytest.config.ExitCode", "_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_terminal_summary", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_unconfigure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.hookspec.pytest_unconfigure", "name": "pytest_unconfigure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_unconfigure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_warning_recorded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["warning_message", "when", "nodeid", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.hookspec.pytest_warning_recorded", "name": "pytest_warning_recorded", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["warning_message", "when", "nodeid", "location"], "arg_types": ["warnings.WarningMessage", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "collect"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "runtest"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_warning_recorded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.hookspec.pytest_warning_recorded", "name": "pytest_warning_recorded", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["warning_message", "when", "nodeid", "location"], "arg_types": ["warnings.WarningMessage", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "collect"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "runtest"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pytest_warning_recorded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\hookspec.py"}