{"data_mtime": 1757534916, "dep_lines": [45, 46, 48, 53, 55, 73, 7, 19, 50, 54, 57, 58, 59, 61, 67, 68, 74, 75, 402, 1152, 1460, 1482, 4, 6, 7, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 36, 38, 49, 1455, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 10, 5, 5, 10, 10, 5, 5, 5, 5, 25, 25, 20, 20, 20, 20, 5, 10, 20, 10, 10, 10, 10, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.compat", "_pytest.config.exceptions", "_pytest.config.findpaths", "_pytest._code.code", "_pytest.config.argparsing", "_pytest.assertion.rewrite", "collections.abc", "importlib.metadata", "_pytest._code", "_pytest._io", "_pytest.deprecated", "_pytest.hookspec", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.cacheprovider", "_pytest.terminal", "_pytest.assertion", "_pytest.helpconfig", "packaging.version", "packaging.requirements", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "collections", "contextlib", "copy", "dataclasses", "enum", "functools", "glob", "importlib", "inspect", "os", "pathlib", "re", "shlex", "sys", "textwrap", "types", "typing", "warnings", "pluggy", "_pytest", "pytest", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_pytest._io.terminalwriter", "abc", "importlib._abc", "importlib.abc", "io", "pluggy._hooks", "pluggy._manager", "pluggy._tracing"], "hash": "370516b018e5bfaa59310bb85aba73c8a045edf5", "id": "_pytest.config", "ignore_all": true, "interface_hash": "e0d39be60ac453e17f5c3d2168635182c73b4bb7", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\config\\__init__.py", "plugin_data": null, "size": 72712, "suppressed": [], "version_id": "1.17.1"}