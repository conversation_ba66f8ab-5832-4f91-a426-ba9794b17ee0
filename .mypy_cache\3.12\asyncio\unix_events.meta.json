{"data_mtime": 1757534915, "dep_lines": [5, 10, 11, 12, 1, 2, 3, 4, 6, 7, 8, 10, 1, 1], "dep_prios": [5, 10, 5, 5, 10, 10, 5, 5, 5, 5, 5, 20, 5, 30], "dependencies": ["collections.abc", "asyncio.events", "asyncio.base_events", "asyncio.selector_events", "sys", "types", "_typeshed", "abc", "socket", "typing", "typing_extensions", "asyncio", "builtins", "_frozen_importlib"], "hash": "60415337cbcee207f5f979dae9567974dc8da307", "id": "asyncio.unix_events", "ignore_all": true, "interface_hash": "df83d5008a72c25c1ae403915f726e3ca5b067c9", "mtime": 1757425220, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\unix_events.pyi", "plugin_data": null, "size": 11658, "suppressed": [], "version_id": "1.17.1"}