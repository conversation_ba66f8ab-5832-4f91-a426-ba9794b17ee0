{"data_mtime": 1757534916, "dep_lines": [4, 13, 14, 15, 16, 1, 3, 5, 6, 7, 8, 9, 11, 17, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "_pytest.config", "_pytest.nodes", "_pytest.stash", "_pytest.tracemalloc", "__future__", "collections", "functools", "gc", "sys", "traceback", "typing", "warnings", "pytest", "builtins", "_frozen_importlib", "_typeshed", "abc", "pluggy", "pluggy._hooks"], "hash": "146ce438f76c65d3eea91de9297cc569f22d6f22", "id": "_pytest.unraisableexception", "ignore_all": true, "interface_hash": "fb4526fae518c8446f48bf8d85ce057e0db162cc", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\unraisableexception.py", "plugin_data": null, "size": 5179, "suppressed": [], "version_id": "1.17.1"}