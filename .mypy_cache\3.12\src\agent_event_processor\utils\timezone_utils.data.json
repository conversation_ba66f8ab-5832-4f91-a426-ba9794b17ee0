{".class": "MypyFile", "_fullname": "src.agent_event_processor.utils.timezone_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Logger": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.logger.Logger", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agent_event_processor.utils.timezone_utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agent_event_processor.utils.timezone_utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agent_event_processor.utils.timezone_utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agent_event_processor.utils.timezone_utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agent_event_processor.utils.timezone_utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agent_event_processor.utils.timezone_utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "convert_to_tenant_timezone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["utc_timestamp", "tenant_timezone"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agent_event_processor.utils.timezone_utils.convert_to_tenant_timezone", "name": "convert_to_tenant_timezone", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["utc_timestamp", "tenant_timezone"], "arg_types": ["datetime.datetime", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_tenant_timezone", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "generate_dimension_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["local_timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agent_event_processor.utils.timezone_utils.generate_dimension_keys", "name": "generate_dimension_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["local_timestamp"], "arg_types": ["datetime.datetime"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_dimension_keys", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_client_timezone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agent_event_processor.utils.timezone_utils.get_client_timezone", "name": "get_client_timezone", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_client_timezone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_settings": {".class": "SymbolTableNode", "cross_ref": "src.agent_event_processor.config.settings.get_settings", "kind": "Gdef"}, "get_shift_date_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["local_timestamp", "shift_start_hour"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agent_event_processor.utils.timezone_utils.get_shift_date_key", "name": "get_shift_date_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["local_timestamp", "shift_start_hour"], "arg_types": ["datetime.datetime", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_shift_date_key", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.agent_event_processor.utils.timezone_utils.logger", "name": "logger", "setter_type": null, "type": "aws_lambda_powertools.logging.logger.Logger"}}, "pytz": {".class": "SymbolTableNode", "cross_ref": "pytz", "kind": "Gdef"}}, "path": "lambdas\\agent-event-processor\\src\\agent_event_processor\\utils\\timezone_utils.py"}