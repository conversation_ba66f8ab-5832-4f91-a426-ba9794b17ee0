{"data_mtime": 1757534916, "dep_lines": [26, 34, 36, 5, 24, 30, 32, 35, 39, 40, 41, 42, 49, 324, 413, 2, 4, 9, 11, 12, 14, 20, 22, 24, 46, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 10, 5, 10, 5, 5, 10, 10, 20, 25, 5, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.compat", "_pytest.mark.structures", "collections.abc", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.main", "_pytest.mark", "_pytest.fixtures", "__future__", "abc", "functools", "os", "pathlib", "typing", "warnings", "pluggy", "_pytest", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "pluggy._hooks", "types"], "hash": "372ae7d00561b2ec94edc79fbec4e82076955ec9", "id": "_pytest.nodes", "ignore_all": true, "interface_hash": "04d3040b205c06e4df78744380dd55fb49d7ba7c", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\nodes.py", "plugin_data": null, "size": 26540, "suppressed": [], "version_id": "1.17.1"}