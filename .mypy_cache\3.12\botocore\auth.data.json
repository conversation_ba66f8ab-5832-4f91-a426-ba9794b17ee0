{".class": "MypyFile", "_fullname": "botocore.auth", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AUTH_PREF_TO_SIGNATURE_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.AUTH_PREF_TO_SIGNATURE_VERSION", "name": "AUTH_PREF_TO_SIGNATURE_VERSION", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AUTH_TYPE_MAPS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.AUTH_TYPE_MAPS", "name": "AUTH_TYPE_MAPS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "botocore.auth.BaseSigner"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AUTH_TYPE_TO_SIGNATURE_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.AUTH_TYPE_TO_SIGNATURE_VERSION", "name": "AUTH_TYPE_TO_SIGNATURE_VERSION", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AWSRequest": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseSigner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.BaseSigner", "name": "BaseSigner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.BaseSigner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRES_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.BaseSigner.REQUIRES_REGION", "name": "REQUIRES_REGION", "setter_type": null, "type": "builtins.bool"}}, "REQUIRES_TOKEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.BaseSigner.REQUIRES_TOKEN", "name": "REQUIRES_TOKEN", "setter_type": null, "type": "builtins.bool"}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.BaseSigner.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.BaseSigner", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of BaseSigner", "ret_type": {".class": "UnionType", "items": ["botocore.awsrequest.AWSRequest", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.BaseSigner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.BaseSigner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BearerAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.<PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.Bear<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.Bear<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.Bear<PERSON>", "botocore.auth.<PERSON>", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.BearerAuth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.Bear<PERSON>", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of BearerAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.BearerAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.Bear<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CRT_AUTH_TYPE_MAPS": {".class": "SymbolTableNode", "cross_ref": "botocore.crt.auth.CRT_AUTH_TYPE_MAPS", "kind": "Gdef"}, "Credentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.Credentials", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EMPTY_SHA256_HASH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.EMPTY_SHA256_HASH", "name": "EMPTY_SHA256_HASH", "setter_type": null, "type": "builtins.str"}}, "HAS_CRT": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.HAS_CRT", "kind": "Gdef"}, "HTTPMessage": {".class": "SymbolTableNode", "cross_ref": "http.client.HTTPMessage", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HmacV1Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.HmacV1Auth", "name": "HmacV1Auth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.HmacV1Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.HmacV1Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "QSAOfInterest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.HmacV1Auth.QSAOfInterest", "name": "QSAOfInterest", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "credentials", "service_name", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "credentials", "service_name", "region_name"], "arg_types": ["botocore.auth.HmacV1Auth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HmacV1Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.HmacV1Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of HmacV1Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_custom_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.canonical_custom_headers", "name": "canonical_custom_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["botocore.auth.HmacV1Auth", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_custom_headers of HmacV1Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "split", "auth_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.canonical_resource", "name": "canonical_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "split", "auth_path"], "arg_types": ["botocore.auth.HmacV1Auth", {".class": "TypeAliasType", "args": [], "type_ref": "urllib.parse.SplitResult"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_resource of HmacV1Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_standard_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.canonical_standard_headers", "name": "canonical_standard_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["botocore.auth.HmacV1Auth", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_standard_headers of HmacV1Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "method", "split", "headers", "expires", "auth_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.canonical_string", "name": "canonical_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "method", "split", "headers", "expires", "auth_path"], "arg_types": ["botocore.auth.HmacV1Auth", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "urllib.parse.SplitResult"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_string of HmacV1Auth", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.auth.HmacV1Auth.credentials", "name": "credentials", "setter_type": null, "type": {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}}}, "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "method", "split", "headers", "expires", "auth_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.get_signature", "name": "get_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "method", "split", "headers", "expires", "auth_path"], "arg_types": ["botocore.auth.HmacV1Auth", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "urllib.parse.SplitResult"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_signature of HmacV1Auth", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string_to_sign"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.sign_string", "name": "sign_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "string_to_sign"], "arg_types": ["botocore.auth.HmacV1Auth", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_string of HmacV1Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unquote_v": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1Auth.unquote_v", "name": "unquote_v", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nv"], "arg_types": ["botocore.auth.HmacV1Auth", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unquote_v of HmacV1Auth", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.HmacV1Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.HmacV1Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HmacV1PostAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.HmacV1Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.HmacV1PostAuth", "name": "HmacV1PostAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.HmacV1PostAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.HmacV1PostAuth", "botocore.auth.HmacV1Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1PostAuth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.HmacV1PostAuth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of HmacV1PostAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.HmacV1PostAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.HmacV1PostAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HmacV1QueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.HmacV1Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.HmacV1QueryAuth", "name": "HmacV1QueryAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.HmacV1QueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.HmacV1QueryAuth", "botocore.auth.HmacV1Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.HmacV1QueryAuth.DEFAULT_EXPIRES", "name": "DEFAULT_EXPIRES", "setter_type": null, "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "credentials", "expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.HmacV1QueryAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "credentials", "expires"], "arg_types": ["botocore.auth.HmacV1QueryAuth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HmacV1QueryAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.auth.HmacV1QueryAuth.credentials", "name": "credentials", "setter_type": null, "type": {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.HmacV1QueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.HmacV1QueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ISO8601": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.ISO8601", "name": "ISO8601", "setter_type": null, "type": "builtins.str"}}, "IdentityCache": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.IdentityCache", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MD5_AVAILABLE": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.MD5_AVAILABLE", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PAYLOAD_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.PAYLOAD_BUFFER", "name": "PAYLOAD_BUFFER", "setter_type": null, "type": "builtins.int"}}, "ReadOnlyCredentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.ReadOnlyCredentials", "kind": "Gdef", "module_hidden": true, "module_public": false}, "S3ExpressAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.S3SigV4Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.S3ExpressAuth", "name": "S3ExpressAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.S3ExpressAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.S3ExpressAuth", "botocore.auth.S3SigV4Auth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRES_IDENTITY_CACHE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.S3ExpressAuth.REQUIRES_IDENTITY_CACHE", "name": "REQUIRES_IDENTITY_CACHE", "setter_type": null, "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 3], "arg_names": ["self", "credentials", "service_name", "region_name", "identity_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.S3ExpressAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3], "arg_names": ["self", "credentials", "service_name", "region_name", "identity_cache"], "arg_types": ["botocore.auth.S3ExpressAuth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "botocore.utils.IdentityCache"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of S3ExpressAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.S3ExpressAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.S3ExpressAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ExpressPostAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.S3ExpressAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.S3ExpressPostAuth", "name": "S3ExpressPostAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.S3ExpressPostAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.S3ExpressPostAuth", "botocore.auth.S3ExpressAuth", "botocore.auth.S3SigV4Auth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.S3ExpressPostAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.S3ExpressPostAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ExpressQueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.S3ExpressAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.S3ExpressQueryAuth", "name": "S3ExpressQueryAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.S3ExpressQueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.S3ExpressQueryAuth", "botocore.auth.S3ExpressAuth", "botocore.auth.S3SigV4Auth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.S3ExpressQueryAuth.DEFAULT_EXPIRES", "name": "DEFAULT_EXPIRES", "setter_type": null, "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 3, 5], "arg_names": ["self", "credentials", "service_name", "region_name", "identity_cache", "expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.S3ExpressQueryAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3, 5], "arg_names": ["self", "credentials", "service_name", "region_name", "identity_cache", "expires"], "arg_types": ["botocore.auth.S3ExpressQueryAuth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "botocore.utils.IdentityCache", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of S3ExpressQueryAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.S3ExpressQueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.S3ExpressQueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3SigV4Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.SigV4Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.S3SigV4Auth", "name": "S3SigV4Auth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.S3SigV4Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.S3SigV4Auth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.S3SigV4Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.S3SigV4Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3SigV4PostAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.SigV4Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.S3SigV4PostAuth", "name": "S3SigV4PostAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.S3SigV4PostAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.S3SigV4PostAuth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.S3SigV4PostAuth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.S3SigV4PostAuth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of S3SigV4PostAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.S3SigV4PostAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.S3SigV4PostAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3SigV4QueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.SigV4QueryAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.S3SigV4QueryAuth", "name": "S3SigV4QueryAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.S3SigV4QueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.S3SigV4QueryAuth", "botocore.auth.SigV4QueryAuth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.S3SigV4QueryAuth.payload", "name": "payload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.S3SigV4QueryAuth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "payload of S3SigV4QueryAuth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.S3SigV4QueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.S3SigV4QueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SIGNED_HEADERS_BLACKLIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.SIGNED_HEADERS_BLACKLIST", "name": "SIGNED_HEADERS_BLACKLIST", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SIGV4_TIMESTAMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.SIGV4_TIMESTAMP", "name": "SIGV4_TIMESTAMP", "setter_type": null, "type": "builtins.str"}}, "STREAMING_UNSIGNED_PAYLOAD_TRAILER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.STREAMING_UNSIGNED_PAYLOAD_TRAILER", "name": "STREAMING_UNSIGNED_PAYLOAD_TRAILER", "setter_type": null, "type": "builtins.str"}}, "SigV2Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.SigV2Auth", "name": "SigV2Auth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.SigV2Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.SigV2Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV2Auth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["botocore.auth.SigV2Auth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SigV2Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV2Auth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV2Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of SigV2Auth", "ret_type": "botocore.awsrequest.AWSRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "calc_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV2Auth.calc_signature", "name": "calc_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "params"], "arg_types": ["botocore.auth.SigV2Auth", "botocore.awsrequest.AWSRequest", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "calc_signature of SigV2Auth", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.auth.SigV2Auth.credentials", "name": "credentials", "setter_type": null, "type": {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.SigV2Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.SigV2Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SigV3Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.SigV3Auth", "name": "SigV3Auth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.SigV3Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.SigV3Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV3Auth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["botocore.auth.SigV3Auth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SigV3Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV3Auth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV3Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of SigV3Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.auth.SigV3Auth.credentials", "name": "credentials", "setter_type": null, "type": {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.SigV3Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.SigV3Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SigV4Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.SigV4Auth", "name": "SigV4Auth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.SigV4Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRES_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.SigV4Auth.REQUIRES_REGION", "name": "REQUIRES_REGION", "setter_type": null, "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "credentials", "service_name", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "credentials", "service_name", "region_name"], "arg_types": ["botocore.auth.SigV4Auth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SigV4Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_auth of SigV4Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers_to_sign"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.canonical_headers", "name": "canonical_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers_to_sign"], "arg_types": ["botocore.auth.SigV4Auth", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_headers of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_query_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.canonical_query_string", "name": "canonical_query_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_query_string of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonical_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.canonical_request", "name": "canonical_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "canonical_request of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credential_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.credential_scope", "name": "credential_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "credential_scope of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.auth.SigV4Auth.credentials", "name": "credentials", "setter_type": null, "type": {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}}}, "headers_to_sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.headers_to_sign", "name": "headers_to_sign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers_to_sign of SigV4Auth", "ret_type": "http.client.HTTPMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.payload", "name": "payload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "payload of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.scope", "name": "scope", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "scope of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "string_to_sign", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "string_to_sign", "request"], "arg_types": ["botocore.auth.SigV4Auth", "builtins.str", "botocore.awsrequest.AWSRequest"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "signature of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signed_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers_to_sign"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.signed_headers", "name": "signed_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers_to_sign"], "arg_types": ["botocore.auth.SigV4Auth", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "signed_headers of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string_to_sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "canonical_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4Auth.string_to_sign", "name": "string_to_sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "canonical_request"], "arg_types": ["botocore.auth.SigV4Auth", "botocore.awsrequest.AWSRequest", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "string_to_sign of SigV4Auth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.SigV4Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.SigV4Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SigV4QueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.SigV4Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.SigV4QueryAuth", "name": "SigV4QueryAuth", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.SigV4QueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.SigV4QueryAuth", "botocore.auth.SigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.auth.SigV4QueryAuth.DEFAULT_EXPIRES", "name": "DEFAULT_EXPIRES", "setter_type": null, "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "credentials", "service_name", "region_name", "expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.SigV4QueryAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "credentials", "service_name", "region_name", "expires"], "arg_types": ["botocore.auth.SigV4QueryAuth", {".class": "UnionType", "items": ["botocore.credentials.Credentials", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SigV4QueryAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.SigV4QueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.SigV4QueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SplitResult": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.SplitResult", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TokenSigner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.auth.<PERSON>", "name": "TokenSigner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.auth.<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.auth", "mro": ["botocore.auth.<PERSON>", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.auth.TokenSigner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "auth_token"], "arg_types": ["botocore.auth.<PERSON>", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TokenSigner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.auth.TokenSigner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.auth.<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNSIGNED_PAYLOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.UNSIGNED_PAYLOAD", "name": "UNSIGNED_PAYLOAD", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.auth.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.auth.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "resolve_auth_scheme_preference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["preference_list", "auth_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "botocore.auth.resolve_auth_scheme_preference", "name": "resolve_auth_scheme_preference", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["preference_list", "auth_options"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_auth_scheme_preference", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_auth_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["auth_trait"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "botocore.auth.resolve_auth_type", "name": "resolve_auth_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["auth_trait"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_auth_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\botocore-stubs\\auth.pyi"}