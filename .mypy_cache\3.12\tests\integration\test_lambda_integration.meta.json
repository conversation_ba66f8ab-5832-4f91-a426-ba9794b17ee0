{"data_mtime": 1757534916, "dep_lines": [3, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["json", "typing", "pytest", "requests", "builtins", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.outcomes", "_typeshed", "abc", "http", "http.cookiejar", "json.decoder", "json.encoder", "requests.api", "requests.auth", "requests.exceptions", "requests.models"], "hash": "7393ef256f88cbfdfa3e43b3826b9f8df451e64b", "id": "tests.integration.test_lambda_integration", "ignore_all": false, "interface_hash": "b23116df4ae78e863a3f951e0571b51e3228f4d3", "mtime": 1757359245, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "lambdas\\agent-event-processor\\tests\\integration\\test_lambda_integration.py", "plugin_data": null, "size": 9039, "suppressed": [], "version_id": "1.17.1"}