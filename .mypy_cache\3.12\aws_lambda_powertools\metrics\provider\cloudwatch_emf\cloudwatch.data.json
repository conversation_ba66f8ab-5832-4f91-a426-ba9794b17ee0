{".class": "MypyFile", "_fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AmazonCloudWatchEMFProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aws_lambda_powertools.metrics.provider.base.BaseProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "name": "AmazonCloudWatchEMFProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch", "mro": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "aws_lambda_powertools.metrics.provider.base.BaseProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "metric_set", "dimension_set", "namespace", "metadata_set", "service", "default_dimensions", "function_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "metric_set", "dimension_set", "namespace", "metadata_set", "service", "default_dimensions", "function_name"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metric_resolutions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider._metric_resolutions", "name": "_metric_resolutions", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_metric_unit_valid_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider._metric_unit_valid_options", "name": "_metric_unit_valid_options", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_metric_units": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider._metric_units", "name": "_metric_units", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "add_cold_start_metric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.add_cold_start_metric", "name": "add_cold_start_metric", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "aws_lambda_powertools.utilities.typing.lambda_context.LambdaContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_cold_start_metric of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.add_dimension", "name": "add_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_dimension of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.add_metadata", "name": "add_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_metadata of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_metric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "unit", "value", "resolution"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.add_metric", "name": "add_metric", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "unit", "value", "resolution"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "builtins.str", {".class": "UnionType", "items": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties.MetricUnit", "builtins.str"], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties.MetricResolution", "builtins.int"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_metric of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.clear_metrics", "name": "clear_metrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear_metrics of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_dimensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.default_dimensions", "name": "default_dimensions", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "dimension_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.dimension_set", "name": "dimension_set", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "flush_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "raise_on_empty_metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.flush_metrics", "name": "flush_metrics", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "raise_on_empty_metrics"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "flush_metrics of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.function_name", "name": "function_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "log_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "lambda_handler", "capture_cold_start_metric", "raise_on_empty_metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.log_metrics", "name": "log_metrics", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "lambda_handler", "capture_cold_start_metric", "raise_on_empty_metrics", "kwargs"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.log_metrics", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "log_metrics of AmazonCloudWatchEMFProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.log_metrics", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "metadata_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.metadata_set", "name": "metadata_set", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "metric_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.metric_set", "name": "metric_set", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.namespace", "name": "namespace", "setter_type": null, "type": "builtins.str"}}, "serialize_metric_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "metrics", "dimensions", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.serialize_metric_set", "name": "serialize_metric_set", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "metrics", "dimensions", "metadata"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialize_metric_set of AmazonCloudWatchEMFProvider", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.types.CloudWatchEMFOutput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.service", "name": "service", "setter_type": null, "type": "builtins.str"}}, "set_default_dimensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "dimensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.set_default_dimensions", "name": "set_default_dimensions", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "dimensions"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_default_dimensions of AmazonCloudWatchEMFProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.set_timestamp", "name": "set_timestamp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timestamp"], "arg_types": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", {".class": "UnionType", "items": ["builtins.int", "datetime.datetime"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_timestamp of AmazonCloudWatchEMFProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.timestamp", "name": "timestamp", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.AmazonCloudWatchEMFProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyCallableT": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.types.AnyCallableT", "kind": "Gdef"}, "BaseProvider": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.base.BaseProvider", "kind": "Gdef"}, "CloudWatchEMFOutput": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.types.CloudWatchEMFOutput", "kind": "Gdef"}, "LambdaContext": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.utilities.typing.lambda_context.LambdaContext", "kind": "Gdef"}, "MAX_DIMENSIONS": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.constants.MAX_DIMENSIONS", "kind": "Gdef"}, "MAX_METRICS": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.constants.MAX_METRICS", "kind": "Gdef"}, "MAX_METRIC_NAME_LENGTH": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.constants.MAX_METRIC_NAME_LENGTH", "kind": "Gdef"}, "MIN_METRIC_NAME_LENGTH": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.constants.MIN_METRIC_NAME_LENGTH", "kind": "Gdef"}, "MetricNameError": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.exceptions.MetricNameError", "kind": "Gdef"}, "MetricNameUnitResolution": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.types.MetricNameUnitResolution", "kind": "Gdef"}, "MetricResolution": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties.MetricResolution", "kind": "Gdef"}, "MetricUnit": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties.MetricUnit", "kind": "Gdef"}, "MetricValueError": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.exceptions.MetricValueError", "kind": "Gdef"}, "PowertoolsUserWarning": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.warnings.PowertoolsUserWarning", "kind": "Gdef"}, "SchemaValidationError": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.exceptions.SchemaValidationError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "constants": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.constants", "kind": "Gdef"}, "convert_timestamp_to_emf_format": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.functions.convert_timestamp_to_emf_format", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "extract_cloudwatch_metric_resolution_value": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.functions.extract_cloudwatch_metric_resolution_value", "kind": "Gdef"}, "extract_cloudwatch_metric_unit_value": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.functions.extract_cloudwatch_metric_unit_value", "kind": "Gdef"}, "is_metrics_disabled": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.functions.is_metrics_disabled", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "numbers": {".class": "SymbolTableNode", "cross_ref": "numbers", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "resolve_cold_start_function_name": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.functions.resolve_cold_start_function_name", "kind": "Gdef"}, "resolve_env_var_choice": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.functions.resolve_env_var_choice", "kind": "Gdef"}, "single_metric": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.base.single_metric", "kind": "Gdef"}, "validate_emf_timestamp": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.metrics.functions.validate_emf_timestamp", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\provider\\cloudwatch_emf\\cloudwatch.py"}