{"data_mtime": 1757534916, "dep_lines": [8, 9, 5, 6, 7, 10, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.shared.user_agent", "aws_lambda_powertools.shared.version", "aws_lambda_powertools.logging", "aws_lambda_powertools.metrics", "aws_lambda_powertools.package_logger", "aws_lambda_powertools.tracing", "pathlib", "builtins", "_frozen_importlib", "abc", "aws_lambda_powertools.shared", "os", "typing"], "hash": "7327633a6f938e11b4b39edeb53b741990bf36db", "id": "aws_lambda_powertools", "ignore_all": true, "interface_hash": "68bf3d24adcab478648fbde68cb0e0907669c3b8", "mtime": 1757425223, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\aws_lambda_powertools\\__init__.py", "plugin_data": null, "size": 676, "suppressed": [], "version_id": "1.17.1"}