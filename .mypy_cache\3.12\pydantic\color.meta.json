{"data_mtime": 1757534914, "dep_lines": [23, 24, 20, 23, 25, 26, 15, 16, 17, 18, 20, 21, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 5, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "pydantic_core.core_schema", "pydantic._internal", "pydantic.json_schema", "pydantic.warnings", "math", "re", "colorsys", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic.annotated_handlers", "types"], "hash": "b3d3a539fad00ced2e1d7315134aa5d3d334f0e7", "id": "pydantic.color", "ignore_all": true, "interface_hash": "d2f87d6d3c8635ef6d528a1d762e7b9602f0614f", "mtime": 1757425225, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\pydantic\\color.py", "plugin_data": null, "size": 21481, "suppressed": [], "version_id": "1.17.1"}