{".class": "MypyFile", "_fullname": "asyncio.proactor_events", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseProactorEventLoop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.base_events.BaseEventLoop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events.BaseProactorEventLoop", "name": "BaseProactorEventLoop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events.BaseProactorEventLoop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events.BaseProactorEventLoop", "asyncio.base_events.BaseEventLoop", "asyncio.events.AbstractEventLoop", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proactor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events.BaseProactorEventLoop.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "proactor"], "arg_types": ["asyncio.proactor_events.BaseProactorEventLoop", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseProactorEventLoop", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sock_recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "asyncio.proactor_events.BaseProactorEventLoop.sock_recv", "name": "sock_recv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "n"], "arg_types": ["asyncio.proactor_events.BaseProactorEventLoop", "socket.socket", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sock_recv of BaseProactorEventLoop", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events.BaseProactorEventLoop.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events.BaseProactorEventLoop", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ProactorBasePipeTransport": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.transports._FlowControlMixin", "asyncio.transports.BaseTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events._ProactorBasePipeTransport", "name": "_ProactorBasePipeTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events._ProactorBasePipeTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports._FlowControlMixin", "asyncio.transports.Transport", "asyncio.transports.ReadTransport", "asyncio.transports.WriteTransport", "asyncio.transports.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events._ProactorBasePipeTransport.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["asyncio.proactor_events._ProactorBasePipeTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__del__ of _ProactorBasePipeTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "loop", "sock", "protocol", "waiter", "extra", "server"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events._ProactorBasePipeTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "loop", "sock", "protocol", "waiter", "extra", "server"], "arg_types": ["asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.events.AbstractEventLoop", "socket.socket", "asyncio.streams.StreamReaderProtocol", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["asyncio.events.AbstractServer", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _ProactorBasePipeTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events._ProactorBasePipeTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events._ProactorBasePipeTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProactorBaseWritePipeTransport": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports.WriteTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events._ProactorBaseWritePipeTransport", "name": "_ProactorBaseWritePipeTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events._ProactorBaseWritePipeTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events._ProactorBaseWritePipeTransport", "asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports._FlowControlMixin", "asyncio.transports.Transport", "asyncio.transports.ReadTransport", "asyncio.transports.WriteTransport", "asyncio.transports.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events._ProactorBaseWritePipeTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events._ProactorBaseWritePipeTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProactorDuplexPipeTransport": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.proactor_events._ProactorReadPipeTransport", "asyncio.proactor_events._ProactorBaseWritePipeTransport", "asyncio.transports.Transport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events._ProactorDuplexPipeTransport", "name": "_ProactorDuplexPipeTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events._ProactorDuplexPipeTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events._ProactorDuplexPipeTransport", "asyncio.proactor_events._ProactorReadPipeTransport", "asyncio.proactor_events._ProactorBaseWritePipeTransport", "asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports._FlowControlMixin", "asyncio.transports.Transport", "asyncio.transports.ReadTransport", "asyncio.transports.WriteTransport", "asyncio.transports.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events._ProactorDuplexPipeTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events._ProactorDuplexPipeTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProactorReadPipeTransport": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports.ReadTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events._ProactorReadPipeTransport", "name": "_ProactorReadPipeTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events._ProactorReadPipeTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events._ProactorReadPipeTransport", "asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports._FlowControlMixin", "asyncio.transports.Transport", "asyncio.transports.ReadTransport", "asyncio.transports.WriteTransport", "asyncio.transports.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "loop", "sock", "protocol", "waiter", "extra", "server", "buffer_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events._ProactorReadPipeTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "loop", "sock", "protocol", "waiter", "extra", "server", "buffer_size"], "arg_types": ["asyncio.proactor_events._ProactorReadPipeTransport", "asyncio.events.AbstractEventLoop", "socket.socket", "asyncio.streams.StreamReaderProtocol", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["asyncio.events.AbstractServer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _ProactorReadPipeTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events._ProactorReadPipeTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events._ProactorReadPipeTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProactorSocketTransport": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.proactor_events._ProactorReadPipeTransport", "asyncio.proactor_events._ProactorBaseWritePipeTransport", "asyncio.transports.Transport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events._ProactorSocketTransport", "name": "_ProactorSocketTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events._ProactorSocketTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events._ProactorSocketTransport", "asyncio.proactor_events._ProactorReadPipeTransport", "asyncio.proactor_events._ProactorBaseWritePipeTransport", "asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports._FlowControlMixin", "asyncio.transports.Transport", "asyncio.transports.ReadTransport", "asyncio.transports.WriteTransport", "asyncio.transports.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "loop", "sock", "protocol", "waiter", "extra", "server"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events._ProactorSocketTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "loop", "sock", "protocol", "waiter", "extra", "server"], "arg_types": ["asyncio.proactor_events._ProactorSocketTransport", "asyncio.events.AbstractEventLoop", "socket.socket", "asyncio.streams.StreamReaderProtocol", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["asyncio.events.AbstractServer", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _ProactorSocketTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sendfile_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "asyncio.proactor_events._ProactorSocketTransport._sendfile_compatible", "name": "_sendfile_compatible", "setter_type": null, "type": "asyncio.constants._SendfileMode"}}, "_set_extra": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events._ProactorSocketTransport._set_extra", "name": "_set_extra", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sock"], "arg_types": ["asyncio.proactor_events._ProactorSocketTransport", "socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_extra of _ProactorSocketTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_write_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.proactor_events._ProactorSocketTransport.can_write_eof", "name": "can_write_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.proactor_events._ProactorSocketTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_write_eof of _ProactorSocketTransport", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events._ProactorSocketTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events._ProactorSocketTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProactorWritePipeTransport": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.proactor_events._ProactorBaseWritePipeTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.proactor_events._ProactorWritePipeTransport", "name": "_ProactorWritePipeTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.proactor_events._ProactorWritePipeTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.proactor_events", "mro": ["asyncio.proactor_events._ProactorWritePipeTransport", "asyncio.proactor_events._ProactorBaseWritePipeTransport", "asyncio.proactor_events._ProactorBasePipeTransport", "asyncio.transports._FlowControlMixin", "asyncio.transports.Transport", "asyncio.transports.ReadTransport", "asyncio.transports.WriteTransport", "asyncio.transports.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.proactor_events._ProactorWritePipeTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.proactor_events._ProactorWritePipeTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.proactor_events.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.proactor_events.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.proactor_events.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.proactor_events.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.proactor_events.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.proactor_events.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.proactor_events.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "base_events": {".class": "SymbolTableNode", "cross_ref": "asyncio.base_events", "kind": "Gdef", "module_hidden": true, "module_public": false}, "constants": {".class": "SymbolTableNode", "cross_ref": "asyncio.constants", "kind": "Gdef", "module_hidden": true, "module_public": false}, "events": {".class": "SymbolTableNode", "cross_ref": "asyncio.events", "kind": "Gdef", "module_hidden": true, "module_public": false}, "futures": {".class": "SymbolTableNode", "cross_ref": "asyncio.futures", "kind": "Gdef", "module_hidden": true, "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket.socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "streams": {".class": "SymbolTableNode", "cross_ref": "asyncio.streams", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "transports": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\proactor_events.pyi"}