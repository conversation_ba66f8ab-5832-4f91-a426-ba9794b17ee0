{".class": "MypyFile", "_fullname": "unittest.loader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TestLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.loader.TestLoader", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unittest.loader.TestLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unittest.loader", "mro": ["unittest.loader.TestLoader", "builtins.object"], "names": {".class": "SymbolTable", "_match_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "path", "full_path", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader._match_path", "name": "_match_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "path", "full_path", "pattern"], "arg_types": ["unittest.loader.TestLoader", "builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_match_path of TestLoader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "discover": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "start_dir", "pattern", "top_level_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader.discover", "name": "discover", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "start_dir", "pattern", "top_level_dir"], "arg_types": ["unittest.loader.TestLoader", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "discover of <PERSON><PERSON><PERSON><PERSON>", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.loader.TestLoader.errors", "name": "errors", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "getTestCaseNames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "testCaseClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader.getTestCaseNames", "name": "getTestCaseNames", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "testCaseClass"], "arg_types": ["unittest.loader.TestLoader", {".class": "TypeType", "item": "unittest.case.TestCase"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getTestCaseNames of TestLoader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loadTestsFromModule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "module", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader.loadTestsFromModule", "name": "loadTestsFromModule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "module", "pattern"], "arg_types": ["unittest.loader.TestLoader", "types.ModuleType", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "loadTestsFromModule of TestLoader", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loadTestsFromName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader.loadTestsFromName", "name": "loadTestsFromName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "module"], "arg_types": ["unittest.loader.TestLoader", "builtins.str", {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "loadTestsFromName of TestLoader", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loadTestsFromNames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "names", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader.loadTestsFromNames", "name": "loadTestsFromNames", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "names", "module"], "arg_types": ["unittest.loader.TestLoader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "loadTestsFromNames of TestLoader", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loadTestsFromTestCase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "testCaseClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unittest.loader.TestLoader.loadTestsFromTestCase", "name": "loadTestsFromTestCase", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "testCaseClass"], "arg_types": ["unittest.loader.TestLoader", {".class": "TypeType", "item": "unittest.case.TestCase"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "loadTestsFromTestCase of TestLoader", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sortTestMethodsUsing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.loader.TestLoader.sortTestMethodsUsing", "name": "sortTestMethodsUsing", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}}}, "suiteClass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.loader.TestLoader.suiteClass", "name": "suiteClass", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SuiteClass"}}}, "testMethodPrefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.loader.TestLoader.testMethodPrefix", "name": "testMethodPrefix", "setter_type": null, "type": "builtins.str"}}, "testNamePatterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.loader.TestLoader.testNamePatterns", "name": "testNamePatterns", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.loader.TestLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.loader.TestLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VALID_MODULE_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "unittest.loader.VALID_MODULE_NAME", "name": "VALID_MODULE_NAME", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_SortComparisonMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "unittest.loader._SortComparisonMethod", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_SuiteClass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "unittest.loader._SuiteClass", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["unittest.case.TestCase"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "defaultTestLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.loader.defaultTestLoader", "name": "defaultTestLoader", "setter_type": null, "type": "unittest.loader.TestLoader"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "findTestCases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["module", "prefix", "sortUsing", "suiteClass"], "dataclass_transform_spec": null, "deprecated": "function unittest.loader.findTestCases is deprecated: Deprecated in Python 3.11; removal scheduled for Python 3.13", "flags": ["is_decorated"], "fullname": "unittest.loader.findTestCases", "name": "findTestCases", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["module", "prefix", "sortUsing", "suiteClass"], "arg_types": ["types.ModuleType", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}, {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SuiteClass"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "findTestCases", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "unittest.loader.findTestCases", "name": "findTestCases", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["module", "prefix", "sortUsing", "suiteClass"], "arg_types": ["types.ModuleType", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}, {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SuiteClass"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "findTestCases", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getTestCaseNames": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["testCaseClass", "prefix", "sortUsing", "testNamePatterns"], "dataclass_transform_spec": null, "deprecated": "function unittest.loader.getTestCaseNames is deprecated: Deprecated in Python 3.11; removal scheduled for Python 3.13", "flags": ["is_decorated"], "fullname": "unittest.loader.getTestCaseNames", "name": "getTestCaseNames", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["testCaseClass", "prefix", "sortUsing", "testNamePatterns"], "arg_types": [{".class": "TypeType", "item": "unittest.case.TestCase"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getTestCaseNames", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "unittest.loader.getTestCaseNames", "name": "getTestCaseNames", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["testCaseClass", "prefix", "sortUsing", "testNamePatterns"], "arg_types": [{".class": "TypeType", "item": "unittest.case.TestCase"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getTestCaseNames", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "makeSuite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["testCaseClass", "prefix", "sortUsing", "suiteClass"], "dataclass_transform_spec": null, "deprecated": "function unittest.loader.makeSuite is deprecated: Deprecated in Python 3.11; removal scheduled for Python 3.13", "flags": ["is_decorated"], "fullname": "unittest.loader.makeSuite", "name": "makeSuite", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["testCaseClass", "prefix", "sortUsing", "suiteClass"], "arg_types": [{".class": "TypeType", "item": "unittest.case.TestCase"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}, {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SuiteClass"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makeSuite", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "unittest.loader.makeSuite", "name": "makeSuite", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["testCaseClass", "prefix", "sortUsing", "suiteClass"], "arg_types": [{".class": "TypeType", "item": "unittest.case.TestCase"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SortComparisonMethod"}, {".class": "TypeAliasType", "args": [], "type_ref": "unittest.loader._SuiteClass"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makeSuite", "ret_type": "unittest.suite.TestSuite", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unittest": {".class": "SymbolTableNode", "cross_ref": "unittest", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\unittest\\loader.pyi"}