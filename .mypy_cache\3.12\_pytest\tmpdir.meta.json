{"data_mtime": 1757534916, "dep_lines": [26, 6, 17, 22, 23, 27, 28, 30, 31, 32, 33, 4, 7, 8, 9, 10, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.pathlib", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.reports", "_pytest.stash", "__future__", "dataclasses", "os", "pathlib", "re", "shutil", "tempfile", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "pluggy", "pluggy._hooks", "posixpath"], "hash": "3d723da5cd41cc99358ea3bdbcadede7f3c046cf", "id": "_pytest.tmpdir", "ignore_all": true, "interface_hash": "d8ecada2389dbdd9a5019242baf1220f3d4996c4", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\tmpdir.py", "plugin_data": null, "size": 11263, "suppressed": [], "version_id": "1.17.1"}