{".class": "MypyFile", "_fullname": "requests.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CookieJar": {".class": "SymbolTableNode", "cross_ref": "http.cookiejar.CookieJar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "requests.models.Response", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Auth": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Auth", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Cert": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Cert", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Data": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Data", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Files": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Files", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_HeadersMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "requests.api._HeadersMapping", "line": 8, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_HooksInput": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._HooksInput", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_JSON": {".class": "SymbolTableNode", "cross_ref": "requests.models._JSON", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Params": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Params", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TextMapping": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._TextMapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Timeout": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Timeout", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Verify": {".class": "SymbolTableNode", "cross_ref": "requests.sessions._Verify", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "head", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "options", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "data", "params", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "data", "params", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "patch", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "data", "json", "params", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.post", "name": "post", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "data", "json", "params", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "data", "params", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "data", "params", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "put", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.api.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "data", "headers", "cookies", "files", "auth", "timeout", "allow_redirects", "proxies", "hooks", "stream", "verify", "cert", "json"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Params"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Data"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.api._HeadersMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["http.cookiejar.CookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Files"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Auth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Timeout"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._TextMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._HooksInput"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Verify"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.sessions._Cert"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.models._JSON"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\requests-stubs\\api.pyi"}