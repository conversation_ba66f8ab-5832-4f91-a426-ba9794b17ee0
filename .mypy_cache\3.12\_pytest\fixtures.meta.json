{"data_mtime": 1757534916, "dep_lines": [40, 56, 63, 8, 37, 38, 42, 43, 53, 57, 60, 61, 64, 67, 69, 72, 81, 2, 4, 5, 16, 17, 18, 19, 20, 21, 22, 23, 34, 36, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 10, 10, 10, 10, 5, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.mark.structures", "collections.abc", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.main", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.warning_types", "_pytest.python", "__future__", "abc", "collections", "dataclasses", "functools", "inspect", "os", "pathlib", "sys", "types", "typing", "warnings", "_pytest", "builtins", "_collections_abc", "_frozen_importlib", "_pytest._io.terminalwriter", "_typeshed", "enum", "pluggy", "pluggy._manager"], "hash": "f8a172bba8735ce0417048d988a0effa9bb2175d", "id": "_pytest.fixtures", "ignore_all": true, "interface_hash": "71387a2ed8e963b9055c13b997d6d2e7e58ea202", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\fixtures.py", "plugin_data": null, "size": 77746, "suppressed": [], "version_id": "1.17.1"}