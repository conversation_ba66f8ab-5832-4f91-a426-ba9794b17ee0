{".class": "MypyFile", "_fullname": "mypy_boto3_redshift_data", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mypy_boto3_redshift_data.Client", "line": 48, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient"}}, "DescribeTablePaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.DescribeTablePaginator", "kind": "Gdef"}, "GetStatementResultPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.GetStatementResultPaginator", "kind": "Gdef"}, "GetStatementResultV2Paginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.GetStatementResultV2Paginator", "kind": "Gdef"}, "ListDatabasesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListDatabasesPaginator", "kind": "Gdef"}, "ListSchemasPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListSchemasPaginator", "kind": "Gdef"}, "ListStatementsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListStatementsPaginator", "kind": "Gdef"}, "ListTablesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.paginator.ListTablesPaginator", "kind": "Gdef"}, "RedshiftDataAPIServiceClient": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_redshift_data.client.RedshiftDataAPIServiceClient", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_redshift_data.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_redshift_data.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy_boto3_redshift_data\\__init__.pyi"}