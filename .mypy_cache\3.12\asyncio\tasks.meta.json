{"data_mtime": 1757534915, "dep_lines": [1, 10, 15, 16, 1, 2, 3, 11, 12, 14, 19, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "collections.abc", "asyncio.events", "asyncio.futures", "concurrent", "sys", "_asyncio", "typing", "typing_extensions", "asyncio", "<PERSON><PERSON><PERSON>", "builtins", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "concurrent.futures._base", "types"], "hash": "9a011349132bd332cdee4c7ec9fb230970d418d9", "id": "asyncio.tasks", "ignore_all": true, "interface_hash": "b71f15062ce7448fe655b3334fb8cfeacc14c9a7", "mtime": 1757425220, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\tasks.pyi", "plugin_data": null, "size": 17125, "suppressed": [], "version_id": "1.17.1"}