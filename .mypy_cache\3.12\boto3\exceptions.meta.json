{"data_mtime": 1757534916, "dep_lines": [9, 7, 9, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 30, 30, 30], "dependencies": ["botocore.exceptions", "typing", "botocore", "builtins", "_frozen_importlib", "abc", "types"], "hash": "2c4d7cb0494f04cbabe942f513fe902bd934543b", "id": "boto3.exceptions", "ignore_all": true, "interface_hash": "64b829511907f6e7bac4f8ec5fa90a92811e56be", "mtime": 1757425226, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\boto3-stubs\\exceptions.pyi", "plugin_data": null, "size": 1339, "suppressed": [], "version_id": "1.17.1"}