{"data_mtime": 1757534916, "dep_lines": [29, 33, 296, 304, 7, 13, 14, 15, 32, 33, 34, 35, 36, 37, 39, 280, 302, 3, 5, 6, 11, 12, 13, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 877, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 5, 10, 10, 10, 5, 20, 5, 5, 5, 5, 5, 20, 20, 5, 10, 5, 10, 10, 20, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._io.saferepr", "_pytest.assertion.util", "importlib.resources.abc", "importlib.resources.readers", "collections.abc", "importlib.abc", "importlib.machinery", "importlib.util", "_pytest._version", "_pytest.assertion", "_pytest.config", "_pytest.fixtures", "_pytest.main", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "importlib.readers", "__future__", "ast", "collections", "errno", "functools", "importlib", "io", "itertools", "marshal", "os", "pathlib", "struct", "sys", "tokenize", "types", "typing", "warnings", "builtins", "_collections_abc", "_frozen_importlib", "_frozen_importlib_external", "_pytest.nodes", "_typeshed", "_warnings", "abc", "importlib._abc", "importlib.resources", "pluggy", "pluggy._tracing", "posixpath"], "hash": "c55fd878631360f191abe238bc4d8bb77201b77f", "id": "_pytest.assertion.rewrite", "ignore_all": true, "interface_hash": "0486be53873a0aec2d0b9352786ce18321385d12", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py", "plugin_data": null, "size": 48636, "suppressed": [], "version_id": "1.17.1"}