{"data_mtime": 1757534914, "dep_lines": [26, 26, 9, 13, 24, 26, 27, 28, 29, 30, 3, 5, 6, 8, 10, 11, 13, 22, 1, 1, 1, 1, 1, 1, 1, 33], "dep_prios": [10, 10, 5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "importlib.metadata", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.json_schema", "pydantic.type_adapter", "__future__", "dataclasses", "re", "functools", "ipaddress", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic_core._pydantic_core", "types"], "hash": "0144005a67236799a563c4b002b4a413df5dedf2", "id": "pydantic.networks", "ignore_all": true, "interface_hash": "712220d48fd7bb3e917f6aee1bd07a0373d076bb", "mtime": 1757425225, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\pydantic\\networks.py", "plugin_data": null, "size": 41446, "suppressed": ["email_validator"], "version_id": "1.17.1"}