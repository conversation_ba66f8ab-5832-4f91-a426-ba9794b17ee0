{".class": "MypyFile", "_fullname": "boto3.resources.factory", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResourceFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.factory.ResourceFactory", "name": "ResourceFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "boto3.resources.factory.ResourceFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.factory", "mro": ["boto3.resources.factory.ResourceFactory", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "emitter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "boto3.resources.factory.ResourceFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "emitter"], "arg_types": ["boto3.resources.factory.ResourceFactory", "botocore.hooks.BaseEventHooks"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResourceFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_from_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "resource_name", "single_resource_json_definition", "service_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "boto3.resources.factory.ResourceFactory.load_from_definition", "name": "load_from_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "resource_name", "single_resource_json_definition", "service_context"], "arg_types": ["boto3.resources.factory.ResourceFactory", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "boto3.utils.ServiceContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_from_definition of ResourceFactory", "ret_type": {".class": "TypeType", "item": "boto3.resources.base.ServiceResource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.factory.ResourceFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.factory.ResourceFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceContext": {".class": "SymbolTableNode", "cross_ref": "boto3.utils.ServiceContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServiceResource": {".class": "SymbolTableNode", "cross_ref": "boto3.resources.base.ServiceResource", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.factory.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\boto3-stubs\\resources\\factory.pyi"}