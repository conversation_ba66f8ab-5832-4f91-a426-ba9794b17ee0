{".class": "MypyFile", "_fullname": "botocore.retries.standard", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseClient": {".class": "SymbolTableNode", "cross_ref": "botocore.client.BaseClient", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseRetryBackoff": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.base.BaseRetryBackoff", "kind": "Gdef"}, "BaseRetryableChecker": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.base.BaseRetryableChecker", "kind": "Gdef"}, "BotoCoreError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.BotoCoreError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "ConnectionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConnectionError", "kind": "Gdef"}, "DEFAULT_MAX_ATTEMPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.retries.standard.DEFAULT_MAX_ATTEMPTS", "name": "DEFAULT_MAX_ATTEMPTS", "setter_type": null, "type": "builtins.int"}}, "ExponentialBackoff": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryBackoff"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.ExponentialBackoff", "name": "ExponentialBackoff", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.ExponentialBackoff", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.ExponentialBackoff", "botocore.retries.base.BaseRetryBackoff", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "max_backoff", "random"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ExponentialBackoff.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "max_backoff", "random"], "arg_types": ["botocore.retries.standard.ExponentialBackoff", "builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ExponentialBackoff", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delay_amount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ExponentialBackoff.delay_amount", "name": "delay_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.ExponentialBackoff", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delay_amount of ExponentialBackoff", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.ExponentialBackoff.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.ExponentialBackoff", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.HTTPClientError", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MaxAttemptsChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryableChecker"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.MaxAttemptsChecker", "name": "MaxAttemptsChecker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.MaxAttemptsChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.MaxAttemptsChecker", "botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.MaxAttemptsChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_attempts"], "arg_types": ["botocore.retries.standard.MaxAttemptsChecker", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MaxAttemptsChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.MaxAttemptsChecker.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.MaxAttemptsChecker", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_retryable of MaxAttemptsChecker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.MaxAttemptsChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.MaxAttemptsChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModeledRetryErrorDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.ModeledRetryErrorDetector", "name": "ModeledRetryErrorDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.ModeledRetryErrorDetector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.ModeledRetryErrorDetector", "builtins.object"], "names": {".class": "SymbolTable", "THROTTLING_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.retries.standard.ModeledRetryErrorDetector.THROTTLING_ERROR", "name": "THROTTLING_ERROR", "setter_type": null, "type": "builtins.str"}}, "TRANSIENT_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.retries.standard.ModeledRetryErrorDetector.TRANSIENT_ERROR", "name": "TRANSIENT_ERROR", "setter_type": null, "type": "builtins.str"}}, "detect_error_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ModeledRetryErrorDetector.detect_error_type", "name": "detect_error_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.ModeledRetryErrorDetector", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "detect_error_type of ModeledRetryErrorDetector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.ModeledRetryErrorDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.ModeledRetryErrorDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModeledRetryableChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryableChecker"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.ModeledRetryableChecker", "name": "ModeledRetryableChecker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.ModeledRetryableChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.ModeledRetryableChecker", "botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ModeledRetryableChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.retries.standard.ModeledRetryableChecker"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModeledRetryableChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ModeledRetryableChecker.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.ModeledRetryableChecker", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_retryable of ModeledRetryableChecker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.ModeledRetryableChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.ModeledRetryableChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperationModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.OperationModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrRetryChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryableChecker"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.OrRetryChecker", "name": "OrRetryChecker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.OrRetryChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.OrRetryChecker", "botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.OrRetryChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkers"], "arg_types": ["botocore.retries.standard.OrRetryChecker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OrRetryChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.OrRetryChecker.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.OrRetryChecker", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_retryable of OrRetryChecker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.OrRetryChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.OrRetryChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ReadTimeoutError", "kind": "Gdef"}, "RetryContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.RetryContext", "name": "RetryContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.RetryContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.RetryContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attempt_number", "operation_model", "parsed_response", "http_response", "caught_exception", "request_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attempt_number", "operation_model", "parsed_response", "http_response", "caught_exception", "request_context"], "arg_types": ["botocore.retries.standard.RetryContext", "builtins.int", {".class": "UnionType", "items": ["botocore.model.OperationModel", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RetryContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_retry_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryContext.add_retry_metadata", "name": "add_retry_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.retries.standard.RetryContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_retry_metadata of RetryContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attempt_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.retries.standard.RetryContext.attempt_number", "name": "attempt_number", "setter_type": null, "type": "builtins.int"}}, "caught_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.retries.standard.RetryContext.caught_exception", "name": "caught_exception", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_error_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryContext.get_error_code", "name": "get_error_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_error_code of RetryContext", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_retry_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryContext.get_retry_metadata", "name": "get_retry_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_retry_metadata of RetryContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "http_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.retries.standard.RetryContext.http_response", "name": "http_response", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "operation_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.retries.standard.RetryContext.operation_model", "name": "operation_model", "setter_type": null, "type": {".class": "UnionType", "items": ["botocore.model.OperationModel", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "parsed_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.retries.standard.RetryContext.parsed_response", "name": "parsed_response", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "request_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.retries.standard.RetryContext.request_context", "name": "request_context", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.RetryContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.RetryContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetryEventAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.RetryEventAdapter", "name": "RetryEventAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.RetryEventAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.RetryEventAdapter", "builtins.object"], "names": {".class": "SymbolTable", "adapt_retry_response_from_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryEventAdapter.adapt_retry_response_from_context", "name": "adapt_retry_response_from_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.RetryEventAdapter", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt_retry_response_from_context of RetryEventAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_retry_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryEventAdapter.create_retry_context", "name": "create_retry_context", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.retries.standard.RetryEventAdapter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_retry_context of RetryEventAdapter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.RetryEventAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.RetryEventAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetryHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.RetryHandler", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.RetryHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.RetryHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "retry_policy", "retry_event_adapter", "retry_quota"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "retry_policy", "retry_event_adapter", "retry_quota"], "arg_types": ["botocore.retries.standard.RetryHandler", "botocore.retries.standard.RetryPolicy", "botocore.retries.standard.RetryEventAdapter", "botocore.retries.quota.RetryQuota"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RetryHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "needs_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryHandler.needs_retry", "name": "needs_retry", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.retries.standard.RetryHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "needs_retry of RetryHandler", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.RetryHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.RetryHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetryPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.RetryPolicy", "name": "RetryPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.RetryPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.RetryPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "retry_checker", "retry_backoff"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "retry_checker", "retry_backoff"], "arg_types": ["botocore.retries.standard.RetryPolicy", "botocore.retries.base.BaseRetryableChecker", "botocore.retries.base.BaseRetryBackoff"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RetryPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryPolicy.compute_retry_delay", "name": "compute_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.RetryPolicy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_retry_delay of RetryPolicy", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryPolicy.should_retry", "name": "should_retry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.RetryPolicy", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_retry of RetryPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.RetryPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.RetryPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetryQuota": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.quota.RetryQuota", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RetryQuotaChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.RetryQuotaChecker", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.RetryQuotaChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.RetryQuotaChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "quota"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryQuotaChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "quota"], "arg_types": ["botocore.retries.standard.RetryQuotaChecker", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RetryQuotaChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acquire_retry_quota": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryQuotaChecker.acquire_retry_quota", "name": "acquire_retry_quota", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.RetryQuotaChecker", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "acquire_retry_quota of Retry<PERSON><PERSON><PERSON><PERSON>hecker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release_retry_quota": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "context", "http_response", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.RetryQuotaChecker.release_retry_quota", "name": "release_retry_quota", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "context", "http_response", "kwargs"], "arg_types": ["botocore.retries.standard.RetryQuotaChecker", "botocore.retries.standard.RetryContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "release_retry_quota of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.RetryQuotaChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.RetryQuotaChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StandardRetryConditions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryableChecker"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.StandardRetryConditions", "name": "StandardRetryConditions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.StandardRetryConditions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.StandardRetryConditions", "botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "max_attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.StandardRetryConditions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "max_attempts"], "arg_types": ["botocore.retries.standard.StandardRetryConditions", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StandardRetryConditions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.StandardRetryConditions.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.StandardRetryConditions", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_retryable of StandardRetryConditions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.StandardRetryConditions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.StandardRetryConditions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThrottledRetryableChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryableChecker"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.ThrottledRetryableChecker", "name": "ThrottledRetryableChecker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.ThrottledRetryableChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.ThrottledRetryableChecker", "botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "throttled_error_codes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ThrottledRetryableChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "throttled_error_codes"], "arg_types": ["botocore.retries.standard.ThrottledRetryableChecker", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ThrottledRetryableChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ThrottledRetryableChecker.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.ThrottledRetryableChecker", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_retryable of ThrottledRetryableChecker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.ThrottledRetryableChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.ThrottledRetryableChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThrottlingErrorDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.ThrottlingErrorDetector", "name": "ThrottlingErrorDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.ThrottlingErrorDetector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.ThrottlingErrorDetector", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_event_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ThrottlingErrorDetector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_event_adapter"], "arg_types": ["botocore.retries.standard.ThrottlingErrorDetector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ThrottlingErrorDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_throttling_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.ThrottlingErrorDetector.is_throttling_error", "name": "is_throttling_error", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.retries.standard.ThrottlingErrorDetector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_throttling_error of ThrottlingErrorDetector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.ThrottlingErrorDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.ThrottlingErrorDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransientRetryableChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.retries.base.BaseRetryableChecker"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.retries.standard.TransientRetryableChecker", "name": "TransientRetryableChecker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.TransientRetryableChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.retries.standard", "mro": ["botocore.retries.standard.TransientRetryableChecker", "botocore.retries.base.BaseRetryableChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "transient_error_codes", "transient_status_codes", "transient_exception_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.TransientRetryableChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "transient_error_codes", "transient_status_codes", "transient_exception_cls"], "arg_types": ["botocore.retries.standard.TransientRetryableChecker", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "botocore.exceptions.BotoCoreError"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TransientRetryableChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "botocore.retries.standard.TransientRetryableChecker.is_retryable", "name": "is_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["botocore.retries.standard.TransientRetryableChecker", "botocore.retries.standard.RetryContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_retryable of TransientRetryableChecker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.retries.standard.TransientRetryableChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.retries.standard.TransientRetryableChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.standard.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.standard.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.standard.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.standard.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.standard.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.retries.standard.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.retries.standard.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "quota": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.quota", "kind": "Gdef"}, "register_retry_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["client", "max_attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "botocore.retries.standard.register_retry_handler", "name": "register_retry_handler", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["client", "max_attempts"], "arg_types": ["botocore.client.BaseClient", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_retry_handler", "ret_type": "botocore.retries.standard.RetryHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "special": {".class": "SymbolTableNode", "cross_ref": "botocore.retries.special", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\botocore-stubs\\retries\\standard.pyi"}