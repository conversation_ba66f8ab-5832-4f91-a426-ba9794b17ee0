{"data_mtime": 1757534916, "dep_lines": [13, 15, 28, 6, 24, 30, 34, 3, 5, 9, 10, 178, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.mark.expression", "_pytest.mark.structures", "_pytest.config.argparsing", "collections.abc", "_pytest.config", "_pytest.stash", "_pytest.nodes", "__future__", "collections", "dataclasses", "typing", "pytest", "builtins", "_frozen_importlib", "_pytest.compat", "abc", "enum", "pluggy", "pluggy._hooks"], "hash": "d96d1fe8ad40530afab223638dceed3ca3164a7e", "id": "_pytest.mark", "ignore_all": true, "interface_hash": "63a0a16a4fdc9d070b941ddf706e79a0a90b1706", "mtime": 1757425218, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\_pytest\\mark\\__init__.py", "plugin_data": null, "size": 9885, "suppressed": [], "version_id": "1.17.1"}