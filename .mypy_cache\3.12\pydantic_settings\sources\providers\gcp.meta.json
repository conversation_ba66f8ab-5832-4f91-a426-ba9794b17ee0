{"data_mtime": **********, "dep_lines": [7, 3, 14, 1, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11, 12, 10], "dep_prios": [5, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25], "dependencies": ["pydantic_settings.sources.providers.env", "collections.abc", "pydantic_settings.main", "__future__", "functools", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.sources.base", "types"], "hash": "297b7e3ef0390b90d460261aede450c1c0805170", "id": "pydantic_settings.sources.providers.gcp", "ignore_all": true, "interface_hash": "1d3c733c497d7f882ae5112051e65cc6063af6a4", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repow_m4dy1u\\py_env-python3.13\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\gcp.py", "plugin_data": null, "size": 5644, "suppressed": ["google.auth.credentials", "google.cloud.secretmanager", "google.auth"], "version_id": "1.17.1"}